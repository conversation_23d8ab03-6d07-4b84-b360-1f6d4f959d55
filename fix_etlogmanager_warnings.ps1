# PowerShell脚本：批量修复ETLogManager.Warn过时方法警告
# 将所有ETLogManager.Warn调用替换为ETLogManager.Warning

Write-Host "开始修复ETLogManager.Warn过时方法警告..." -ForegroundColor Green

# 定义需要处理的文件列表（基于编译警告）
$filesToProcess = @(
    "ExtensionsTools\ExtensionsTools\ETOAAutomation\Helpers\ETOAAutoReloginHelper.cs",
    "ExtensionsTools\ExtensionsTools\ETOAAutomation\Helpers\ETOAJsonHelper.cs",
    "ExtensionsTools\ExtensionsTools\ETOAAutomation\Helpers\ETOARetryHelper.cs",
    "ExtensionsTools\ExtensionsTools\ETOAAutomation\Helpers\ETOAStorageHelper.cs",
    "ExtensionsTools\ExtensionsTools\ETOAAutomation\Helpers\ETOACookieHelper.cs",
    "ExtensionsTools\ExtensionsTools\ETOAAutomation\Helpers\ETOAPerformanceHelper.cs"
)

# 定义替换规则
$replacements = @{
    'ETLogManager\.Warn\(' = 'ETLogManager.Warning('
}

$totalFiles = 0
$processedFiles = 0

foreach ($file in $filesToProcess) {
    $fullPath = Join-Path $PSScriptRoot $file
    
    if (Test-Path $fullPath) {
        Write-Host "处理文件: $file" -ForegroundColor Yellow
        $totalFiles++
        
        try {
            $content = Get-Content $fullPath -Raw -Encoding UTF8
            $originalContent = $content
            
            # 应用所有替换规则
            foreach ($pattern in $replacements.Keys) {
                $replacement = $replacements[$pattern]
                $content = $content -replace $pattern, $replacement
            }
            
            # 如果内容有变化，则写回文件
            if ($content -ne $originalContent) {
                Set-Content $fullPath -Value $content -Encoding UTF8 -NoNewline
                $processedFiles++
                Write-Host "  ✓ 已修复" -ForegroundColor Green
            } else {
                Write-Host "  - 无需修复" -ForegroundColor Gray
            }
        }
        catch {
            Write-Host "  ✗ 处理失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "文件不存在: $file" -ForegroundColor Red
    }
}

Write-Host "`n修复完成！" -ForegroundColor Green
Write-Host "总文件数: $totalFiles" -ForegroundColor Cyan
Write-Host "已处理: $processedFiles" -ForegroundColor Cyan
Write-Host "请重新编译项目以验证修复结果。" -ForegroundColor Yellow
