# 🚀 ETOAAutomation - OA系统自动化辅助库

## 📋 项目简介

ETOAAutomation是一个专为OA系统自动化操作设计的C#辅助库，基于CefSharp和Flurl.Http技术栈，提供完整的登录认证、API交互、文件上传、会话管理和网页自动化操作功能。

**🎯 当前状态**: 项目已完成核心架构设计，部分核心模块已精简优化，正在持续开发中。

## 🎯 核心特性

- **🔐 智能登录认证** - 基于CefSharp的自动化登录，支持验证码处理
- **🌐 强大API交互** - 基于Flurl.Http的现代化HTTP客户端
- **📁 简化文件上传** - ✅ **已精简优化** - 专注于基本的单文件上传功能，去除复杂特性
- **🔄 会话状态管理** - 自动维护登录状态，支持断线重连
- **🎮 网页自动化操作** - 支持DOM操作和坐标操作两种模式
- **💾 本地数据存储** - 加密存储认证信息，支持多用户管理
- **🔧 ExtensionsTools集成** - 充分利用现有的配置、日志、异常处理模块

## 🔄 最新更新 (精简优化版本)

### ✅ 已完成精简的核心模块

#### 📁 ETOAFileUploader - 简化文件上传器
- **精简策略**: 移除复杂的分块上传、断点续传、并发上传等高级功能
- **保留功能**: 基本的单文件上传、文件验证、重试机制、进度监控
- **优化特点**:
  - 代码量减少约60%，从700+行精简到292行
  - 专注于核心上传功能，提高稳定性和可维护性
  - 保持完整的错误处理和日志记录

#### 🔧 ETOAUploadConfigHelper - 配置管理助手
- **精简策略**: 移除复杂的高级配置项，保留基础必要配置
- **保留功能**: 文件大小限制、重试次数、超时设置、文件类型限制
- **优化特点**:
  - 配置项从15+个精简到6个核心配置
  - 简化配置文件结构，提高配置管理效率
  - 保持与ETIniFile的完整集成

#### 🎛️ ETOAUploadConfigForm - 配置界面窗体
- **精简策略**: 禁用复杂功能的UI控件，保留基础配置界面
- **保留功能**: 基础上传参数配置、文件类型管理、配置重置
- **优化特点**:
  - 界面简洁明了，用户体验更好
  - 明确标识已简化的功能项
  - 保持完整的配置验证和保存功能

## 📁 项目结构

```
ETOAAutomation/
├── 📄 README.md                        # 项目说明文档
├── 📄 开发规划.md                       # 详细开发规划
├── 📄 技术手册-Flurl.Http和CefSharp.md   # 技术参考手册
├── 📄 环境配置和依赖.md                  # 环境配置指南
│
├── 🔧 核心模块/
│   ├── ETOAClient.cs                   # 主要OA客户端类
│   ├── ETOALoginBrowser.cs             # 登录认证浏览器
│   ├── ETOAApiClient.cs                # API交互客户端
│   ├── ETOASessionManager.cs           # 会话状态管理器
│   ├── ETOAFileUploader.cs             # 文件上传处理器
│   └── ETOASimulationBrowser.cs        # 模拟操作浏览器
│
├── 📊 数据模型/
│   ├── Models/
│   │   ├── ETOALoginInfo.cs            # 登录信息模型
│   │   ├── ETOAApiRequest.cs           # API请求模型
│   │   ├── ETOAApiResponse.cs          # API响应模型
│   │   ├── ETOAUploadResult.cs         # 上传结果模型
│   │   └── ETOASessionData.cs          # 会话数据模型
│
├── 🛠️ 辅助工具/
│   ├── Helpers/
│   │   ├── ETOAJsonHelper.cs           # JSON处理辅助
│   │   ├── ETOACookieHelper.cs         # Cookie处理辅助
│   │   ├── ETOAConfigHelper.cs         # 配置管理辅助
│   │   └── ETOAStorageHelper.cs        # 本地存储辅助
│
├── 💾 存储管理/
│   ├── Storage/
│   │   ├── ETOAAuthStorage.cs          # 认证信息存储
│   │   └── ETOASessionStorage.cs       # 会话状态存储
│
└── 📚 使用示例/
    ├── Examples/
    │   ├── ETOABasicExample.cs         # 基础使用示例
    │   └── ETOAAdvancedExample.cs      # 高级功能示例
```

## 🚀 快速开始

### 1. 环境准备
```csharp
// 安装必要的NuGet包
Install-Package CefSharp.WinForms -Version 126.2.180
Install-Package Flurl.Http -Version 4.0.2
Install-Package Newtonsoft.Json -Version 13.0.3
```

### 2. 基础使用示例
```csharp
// 创建OA客户端
var oaClient = new ETOAClient("https://oa.company.com");

// 配置登录信息
await oaClient.LoginAsync("username", "password");

// 调用API
var result = await oaClient.GetApiDataAsync<UserInfo>("/api/user/info");

// 简化文件上传（精简版本）
var uploadResult = await oaClient.UploadFileAsync("/api/upload",
    filePath, new { category = "document", title = "测试文档" });

// 直接使用文件上传器（精简版本）
var apiClient = new ETOAApiClient("https://oa.company.com");
var fileUploader = new ETOAFileUploader(apiClient);

// 配置基础参数
fileUploader.MaxFileSize = 50; // 50MB
fileUploader.RetryCount = 3;
fileUploader.AllowedExtensions = new[] { ".pdf", ".doc", ".docx", ".jpg", ".png" };

// 执行上传
var result = await fileUploader.UploadFileAsync("/api/upload", @"C:\test.pdf");
```

### 3. 高级自动化操作
```csharp
// 创建模拟操作浏览器
var browser = new ETOASimulationBrowser("https://oa.company.com");

// DOM操作方式
await browser.FillTextAsync("#username", "用户名");
await browser.ClickElementAsync("#loginBtn");

// 坐标操作方式
await browser.ClickAtAsync(100, 200);
await browser.SendKeysAsync("Hello World");

// 等待页面加载完成
await browser.WaitForElementAsync(".dashboard", 10000);
```

## 🔧 配置说明

### 基础配置
```ini
[OA]
BaseUrl=https://oa.company.com
LoginPath=/login
ApiPath=/api

[Network]
TimeoutSeconds=30
MaxRetries=3
AutoRetry=true

[Browser]
UserAgent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
EnableJavaScript=true
EnableCookies=true

[Storage]
EncryptionEnabled=true
SessionTimeout=3600
AutoCleanup=true
```

### 文件上传配置（精简版本）
```ini
[Upload]
MaxFileSize=100
RetryCount=3
RequestTimeout=300

[Restrictions]
AllowedExtensions=.jpg,.jpeg,.png,.gif,.bmp,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.zip,.rar
```

### 配置管理示例
```csharp
// 初始化上传配置
ETOAUploadConfigHelper.Initialize();

// 设置基础配置
ETOAUploadConfigHelper.SetMaxFileSize(50); // 50MB
ETOAUploadConfigHelper.SetRetryCount(5);
ETOAUploadConfigHelper.SetRequestTimeout(600); // 10分钟

// 管理文件类型
ETOAUploadConfigHelper.AddAllowedExtension(".mp4");
ETOAUploadConfigHelper.RemoveAllowedExtension(".exe");

// 应用配置到上传器
var uploader = new ETOAFileUploader(apiClient);
ETOAUploadConfigHelper.ApplyConfigToUploader(uploader);

// 显示配置界面
ETOAUploadConfigForm.ShowConfigDialog();
```

### 日志配置
```csharp
// 使用ExtensionsTools的日志管理
ETLogManager.Info("OA客户端初始化完成");
ETLogManager.Error("API请求失败", exception);
```

## 📖 详细文档

- **[开发规划.md](开发规划.md)** - 完整的开发计划和里程碑
- **[技术手册-Flurl.Http和CefSharp.md](技术手册-Flurl.Http和CefSharp.md)** - 技术实现参考
- **[环境配置和依赖.md](环境配置和依赖.md)** - 环境搭建和部署指南

## 🎯 主要功能模块

### 🔐 ETOALoginBrowser - 登录认证
- 自动化OA系统登录流程
- 支持验证码识别和处理
- 完整的认证信息提取
- 多账户管理支持

### 🌐 ETOAApiClient - API交互
- 基于Flurl.Http的现代化HTTP客户端
- 自动JSON序列化/反序列化
- 智能错误处理和重试机制
- Cookie和认证头自动管理

### 📁 ETOAFileUploader - 文件上传 ✅ **已精简优化**
- ✅ 单文件上传功能（保留）
- ✅ 基础进度监控（保留）
- ✅ 表单数据同步发送（保留）
- ❌ 批量上传功能（已移除）
- ❌ 断点续传支持（已移除）
- ❌ 并发上传控制（已移除）
- ❌ 文件完整性校验（已移除）

### 🔄 ETOASessionManager - 会话管理
- 登录状态实时监控
- 定期心跳维护机制
- 自动重新登录功能
- 会话数据持久化

### 🎮 ETOASimulationBrowser - 自动化操作
- 内嵌Chromium浏览器
- DOM操作和坐标操作双模式
- 丰富的事件系统
- 操作录制和回放功能

### 💾 本地存储系统
- 加密存储认证信息
- 多用户会话管理
- 自动清理过期数据
- 安全的密钥管理

## 🛡️ 安全特性

- **数据加密** - 本地存储的敏感信息采用加密保护
- **会话安全** - 自动检测和处理会话过期
- **错误处理** - 完善的异常处理和错误恢复机制
- **日志记录** - 详细的操作日志，便于问题排查

## 🔧 扩展性设计

- **插件架构** - 支持自定义扩展和插件开发
- **配置驱动** - 通过配置文件灵活控制行为
- **事件机制** - 丰富的事件回调和通知系统
- **接口抽象** - 清晰的接口定义，便于功能扩展

## 📈 性能优化

- **连接复用** - HTTP连接池管理，提高请求效率
- **智能缓存** - 合理的数据缓存策略
- **资源管理** - 自动资源清理，防止内存泄漏
- **并发控制** - 合理的并发请求控制

## 🚨 注意事项

### 精简版本说明
- **功能简化** - 当前版本专注于核心功能，移除了复杂的高级特性
- **性能优化** - 通过精简减少了代码复杂度，提高了稳定性和可维护性
- **渐进开发** - 后续可根据实际需求逐步添加高级功能

### 兼容性说明
- **不影响现有模块** - 本项目作为独立模块，不会修改现有的ETLoginWebBrowser等模块
- **ExtensionsTools集成** - 充分利用现有的ETIniFile、ETLogManager、ETException等功能
- **向后兼容** - 保持与现有代码的完全兼容性

### 系统要求
- **.NET Framework 4.7.2+** 或 **.NET Core 3.1+**
- **Windows 10/11** (64位)
- **Visual C++ Redistributable** 2019或更新版本
- **至少2GB可用内存**

### 部署要求
- 确保所有CefSharp运行时文件正确部署
- 配置正确的应用程序权限
- 网络连接能够访问目标OA系统

## 🤝 贡献指南

1. **代码规范** - 遵循现有的代码风格和命名约定
2. **文档更新** - 新功能需要同步更新相关文档
3. **测试覆盖** - 确保新功能有适当的测试覆盖
4. **向后兼容** - 保持与现有代码的兼容性

## 📞 技术支持

如果在使用过程中遇到问题，请：

1. 查看相关文档和示例代码
2. 检查日志文件中的错误信息
3. 确认环境配置是否正确
4. 联系技术支持团队

## 📄 许可证

本项目遵循与ExtensionsTools相同的许可证协议。

## 🎯 精简版本使用指南

### 快速上手文件上传功能

#### 1. 基础文件上传
```csharp
// 创建API客户端
var apiClient = new ETOAApiClient("https://oa.company.com");

// 创建文件上传器（精简版本）
var fileUploader = new ETOAFileUploader(apiClient);

// 配置基础参数
fileUploader.MaxFileSize = 50; // 50MB
fileUploader.RetryCount = 3;
fileUploader.AllowedExtensions = new[] { ".pdf", ".doc", ".docx", ".jpg", ".png" };

// 执行上传
var result = await fileUploader.UploadFileAsync("/api/upload", @"C:\test.pdf");

if (result.IsSuccess)
{
    Console.WriteLine($"上传成功！文件ID: {result.FileId}");
}
else
{
    Console.WriteLine($"上传失败: {result.Message}");
}
```

#### 2. 使用配置管理
```csharp
// 初始化配置
ETOAUploadConfigHelper.Initialize();

// 设置全局配置
ETOAUploadConfigHelper.SetMaxFileSize(100); // 100MB
ETOAUploadConfigHelper.SetRetryCount(5);
ETOAUploadConfigHelper.SetRequestTimeout(600); // 10分钟

// 管理允许的文件类型
ETOAUploadConfigHelper.AddAllowedExtension(".mp4");
ETOAUploadConfigHelper.RemoveAllowedExtension(".exe");

// 创建上传器并应用配置
var uploader = new ETOAFileUploader(apiClient);
ETOAUploadConfigHelper.ApplyConfigToUploader(uploader);
```

#### 3. 显示配置界面
```csharp
// 显示配置对话框
var result = ETOAUploadConfigForm.ShowConfigDialog();
if (result == DialogResult.OK)
{
    // 用户保存了配置
    Console.WriteLine("配置已更新");
}
```

### 精简版本特点

#### ✅ 保留的核心功能
- **单文件上传**: 稳定可靠的基础上传功能
- **文件验证**: 大小、类型、存在性验证
- **重试机制**: 智能重试，提高成功率
- **配置管理**: 灵活的参数配置和持久化
- **错误处理**: 完善的异常处理和日志记录
- **ExtensionsTools集成**: 充分利用现有基础设施

#### ❌ 移除的复杂功能
- **批量上传**: 简化为单文件上传，避免复杂性
- **断点续传**: 移除分块和续传逻辑
- **并发控制**: 去除并发上传管理
- **完整性校验**: 移除MD5/SHA1校验
- **复杂进度监控**: 简化进度回调机制

#### 🎯 适用场景
- **基础文件上传需求**: 适合大多数常见的文件上传场景
- **快速集成**: 代码简洁，易于理解和集成
- **稳定性优先**: 专注核心功能，减少潜在问题
- **渐进式开发**: 可作为基础版本，后续按需扩展

---

**ETOAAutomation** - 让OA系统自动化变得简单高效！ 🚀

**精简版本** - 专注核心功能，稳定可靠，易于维护！ ✨
