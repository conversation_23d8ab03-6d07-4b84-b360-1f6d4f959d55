using System;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.IO;
using ET.ETOAAutomation.Models;
using ET.ETOAAutomation.Helpers;
using ET.ETOAAutomation.Storage;
using ET;
using System.Linq;

namespace ET.ETOAAutomation
{
    /// <summary>
    /// OA系统自动化客户端主类，整合所有功能模块，提供统一接口
    /// </summary>
    public class ETOAClient : IDisposable
    {
        #region 私有字段

        private ETOALoginBrowser _loginBrowser;
        private ETOAApiClient _apiClient;
        private ETOASessionManager _sessionManager;
        private ETOAFileUploader _fileUploader;
        private ETOASimulationBrowser _simulationBrowser;

        // ETOAConfigHelper是静态类，不需要实例化 ETOAPerformanceHelper是静态类，不需要实例化
        private ETIniFile _configFile;

        private bool _disposed = false;
        private readonly object _lockObject = new object();
        private string _currentUsername;
        private readonly Dictionary<string, object> _globalSettings;

        // 全局异常处理器注册控制
        private static bool _globalExceptionHandlerRegistered = false;

        private static readonly object _registrationLock = new object();

        #endregion 私有字段

        #region 公共属性

        /// <summary>
        /// OA系统基础URL
        /// </summary>
        public string BaseUrl { get; set; }

        /// <summary>
        /// 登录状态
        /// </summary>
        public bool IsLoggedIn { get; private set; }

        /// <summary>
        /// 登录信息
        /// </summary>
        public ETOALoginInfo LoginInfo { get; private set; }

        /// <summary>
        /// API客户端
        /// </summary>
        public ETOAApiClient ApiClient => _apiClient;

        /// <summary>
        /// 会话管理器
        /// </summary>
        public ETOASessionManager SessionManager => _sessionManager;

        /// <summary>
        /// 文件上传器
        /// </summary>
        public ETOAFileUploader FileUploader => _fileUploader;

        /// <summary>
        /// 检查API客户端是否可用
        /// </summary>
        public bool IsApiClientAvailable => _apiClient != null;

        /// <summary>
        /// 检查会话管理器是否可用
        /// </summary>
        public bool IsSessionManagerAvailable => _sessionManager != null;

        /// <summary>
        /// 检查文件上传器是否可用
        /// </summary>
        public bool IsFileUploaderAvailable => _fileUploader != null;

        /// <summary>
        /// 模拟操作浏览器
        /// </summary>
        public ETOASimulationBrowser SimulationBrowser => _simulationBrowser;

        /// <summary>
        /// 当前用户名
        /// </summary>
        public string CurrentUsername => _currentUsername;

        /// <summary>
        /// 全局设置
        /// </summary>
        public IReadOnlyDictionary<string, object> GlobalSettings => _globalSettings;

        #endregion 公共属性

        #region 事件定义

        /// <summary>
        /// 全局异常事件
        /// </summary>
        public event EventHandler<GlobalExceptionEventArgs> GlobalException;

        /// <summary>
        /// 登录状态变更事件
        /// </summary>
        public event EventHandler<LoginStatusChangedEventArgs> LoginStatusChanged;

        /// <summary>
        /// 性能监控事件
        /// </summary>
        public event EventHandler<PerformanceEventArgs> PerformanceAlert;

        #endregion 事件定义

        #region 构造函数

        /// <summary>
        /// 初始化ETOAClient
        /// </summary>
        /// <param name="baseUrl">OA系统基础URL</param>
        public ETOAClient(string baseUrl)
        {
            BaseUrl = baseUrl ?? throw new ArgumentNullException(nameof(baseUrl));
            _globalSettings = new Dictionary<string, object>();

            try
            {
                InitializeComponents();
                InitializeGlobalExceptionHandling();
                ETLogManager.Info($"ETOAClient初始化完成，BaseUrl: {baseUrl}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOAClient初始化失败", ex);
                throw new ETException("ETOAClient初始化失败", "Constructor", ex);
            }
        }

        #endregion 构造函数

        #region 私有方法

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponents()
        {
            var initializationResults = new Dictionary<string, bool>();
            var initializationErrors = new List<string>();

            try
            {
                // 初始化配置文件
                try
                {
                    string configPath = System.IO.Path.Combine(
                        System.IO.Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location),
                        "Config", "ETOAAutomation.ini");
                    _configFile = new ETIniFile(configPath);
                    initializationResults["ConfigFile"] = true;
                    ETLogManager.Info("配置文件初始化成功");
                }
                catch (Exception ex)
                {
                    initializationResults["ConfigFile"] = false;
                    initializationErrors.Add($"配置文件初始化失败: {ex.Message}");
                    ETLogManager.Warning("配置文件初始化失败，将使用默认配置", ex);
                }

                // ETOAConfigHelper是静态类，调用Initialize方法初始化
                try
                {
                    ETOAConfigHelper.Initialize();
                    initializationResults["ConfigHelper"] = true;
                    ETLogManager.Info("配置助手初始化成功");
                }
                catch (Exception ex)
                {
                    initializationResults["ConfigHelper"] = false;
                    initializationErrors.Add($"配置助手初始化失败: {ex.Message}");
                    ETLogManager.Warning("配置助手初始化失败", ex);
                }

                // 初始化API客户端（核心组件，必须成功）
                try
                {
                    _apiClient = new ETOAApiClient(BaseUrl);
                    initializationResults["ApiClient"] = true;
                    ETLogManager.Info("API客户端初始化成功");
                }
                catch (Exception ex)
                {
                    initializationResults["ApiClient"] = false;
                    initializationErrors.Add($"API客户端初始化失败: {ex.Message}");
                    ETLogManager.Error("API客户端初始化失败", ex);
                    throw new ETException("API客户端初始化失败，无法继续", "InitializeComponents", ex);
                }

                // 初始化会话管理器（依赖API客户端）
                try
                {
                    _sessionManager = new ETOASessionManager(_apiClient, BaseUrl);
                    initializationResults["SessionManager"] = true;
                    ETLogManager.Info("会话管理器初始化成功");
                }
                catch (Exception ex)
                {
                    initializationResults["SessionManager"] = false;
                    initializationErrors.Add($"会话管理器初始化失败: {ex.Message}");
                    ETLogManager.Warning("会话管理器初始化失败，会话功能将不可用", ex);
                }

                // 初始化文件上传器（依赖API客户端）
                try
                {
                    _fileUploader = new ETOAFileUploader(_apiClient);
                    initializationResults["FileUploader"] = true;
                    ETLogManager.Info("文件上传器初始化成功");
                }
                catch (Exception ex)
                {
                    initializationResults["FileUploader"] = false;
                    initializationErrors.Add($"文件上传器初始化失败: {ex.Message}");
                    ETLogManager.Warning("文件上传器初始化失败，文件上传功能将不可用", ex);
                }

                // 订阅事件
                try
                {
                    SubscribeToEvents();
                    initializationResults["EventSubscription"] = true;
                    ETLogManager.Info("事件订阅成功");
                }
                catch (Exception ex)
                {
                    initializationResults["EventSubscription"] = false;
                    initializationErrors.Add($"事件订阅失败: {ex.Message}");
                    ETLogManager.Warning("事件订阅失败，部分事件通知将不可用", ex);
                }

                // 记录初始化结果
                var successCount = initializationResults.Values.Count(r => r);
                var totalCount = initializationResults.Count;

                ETLogManager.Info($"ETOAClient组件初始化完成: {successCount}/{totalCount} 个组件成功初始化");

                if (initializationErrors.Count > 0)
                {
                    ETLogManager.Warning($"部分组件初始化失败，但系统仍可正常运行。失败详情: {string.Join("; ", initializationErrors)}");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOAClient组件初始化过程中发生严重错误", ex);
                throw;
            }
        }

        /// <summary>
        /// 初始化全局异常处理
        /// </summary>
        private void InitializeGlobalExceptionHandling()
        {
            lock (_registrationLock)
            {
                if (!_globalExceptionHandlerRegistered)
                {
                    try
                    {
                        // 设置全局异常处理器
                        AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
                        _globalExceptionHandlerRegistered = true;

                        ETLogManager.Info("全局异常处理初始化完成");
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error("初始化全局异常处理失败", ex);
                    }
                }
                else
                {
                    ETLogManager.Info("全局异常处理器已注册，跳过重复注册");
                }
            }
        }

        /// <summary>
        /// 订阅事件
        /// </summary>
        private void SubscribeToEvents()
        {
            try
            {
                // 订阅会话管理器事件
                if (_sessionManager != null)
                {
                    _sessionManager.SessionStatusChanged += OnSessionStatusChanged;
                    _sessionManager.HeartbeatFailed += OnHeartbeatFailed;
                    _sessionManager.AutoReloginAttempted += OnAutoReloginAttempted;
                }

                // ETOAPerformanceHelper是静态类，暂时不支持事件订阅

                ETLogManager.Info("事件订阅完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("订阅事件失败", ex);
            }
        }

        /// <summary>
        /// 全局异常处理
        /// </summary>
        private void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            try
            {
                var exception = e.ExceptionObject as Exception;
                ETLogManager.Error("发生未处理的异常", exception);
                HandleGlobalException(exception, "全局异常处理器");
            }
            catch
            {
                // 避免在异常处理中再次抛出异常
            }
        }

        /// <summary>
        /// 处理全局异常
        /// </summary>
        private void HandleGlobalException(Exception ex, string source)
        {
            try
            {
                var args = new GlobalExceptionEventArgs
                {
                    Exception = ex,
                    Source = source,
                    Timestamp = DateTime.Now,
                    Handled = false
                };

                GlobalException?.Invoke(this, args);

                if (!args.Handled)
                {
                    ETLogManager.Error($"未处理的全局异常 - 来源: {source}", ex);
                }
            }
            catch
            {
                // 避免在异常处理中再次抛出异常
            }
        }

        /// <summary>
        /// 会话状态变更事件处理
        /// </summary>
        private void OnSessionStatusChanged(object sender, SessionStatusChangedEventArgs e)
        {
            try
            {
                ETLogManager.Info($"会话状态变更: {e.OldStatus} -> {e.NewStatus}");

                var args = new LoginStatusChangedEventArgs
                {
                    IsLoggedIn = e.NewStatus == SessionStatus.Active,
                    Username = _currentUsername,
                    Timestamp = e.Timestamp
                };

                LoginStatusChanged?.Invoke(this, args);
            }
            catch (Exception ex)
            {
                HandleGlobalException(ex, "会话状态变更事件处理");
            }
        }

        /// <summary>
        /// 心跳失败事件处理
        /// </summary>
        private void OnHeartbeatFailed(object sender, HeartbeatFailedEventArgs e)
        {
            try
            {
                ETLogManager.Warning($"心跳失败: {e.Reason}");
            }
            catch (Exception ex)
            {
                HandleGlobalException(ex, "心跳失败事件处理");
            }
        }

        /// <summary>
        /// 自动重登事件处理
        /// </summary>
        private void OnAutoReloginAttempted(object sender, AutoReloginEventArgs e)
        {
            try
            {
                if (e.Success)
                {
                    ETLogManager.Info($"自动重登成功，第 {e.AttemptNumber} 次尝试");
                }
                else
                {
                    ETLogManager.Warning($"自动重登失败，第 {e.AttemptNumber} 次尝试: {e.Reason}");
                }
            }
            catch (Exception ex)
            {
                HandleGlobalException(ex, "自动重登事件处理");
            }
        }

        /// <summary>
        /// 性能警报事件处理
        /// </summary>
        private void OnPerformanceAlert(object sender, PerformanceEventArgs e)
        {
            try
            {
                ETLogManager.Warning($"性能警报: {e.Message}");
                PerformanceAlert?.Invoke(this, e);
            }
            catch (Exception ex)
            {
                HandleGlobalException(ex, "性能警报事件处理");
            }
        }

        #endregion 私有方法

        #region 公共方法

        /// <summary>
        /// 获取组件状态信息
        /// </summary>
        /// <returns>组件状态字典</returns>
        public Dictionary<string, bool> GetComponentStatus()
        {
            return new Dictionary<string, bool>
            {
                ["ApiClient"] = IsApiClientAvailable,
                ["SessionManager"] = IsSessionManagerAvailable,
                ["FileUploader"] = IsFileUploaderAvailable,
                ["ConfigFile"] = _configFile != null,
                ["LoginBrowser"] = _loginBrowser != null,
                ["SimulationBrowser"] = _simulationBrowser != null
            };
        }

        /// <summary>
        /// 检查核心功能是否可用
        /// </summary>
        /// <returns>核心功能是否可用</returns>
        public bool IsCoreFunctionalityAvailable()
        {
            return IsApiClientAvailable; // API客户端是核心功能
        }

        /// <summary>
        /// 登录OA系统
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        /// <returns>登录是否成功</returns>
        public async Task<bool> LoginAsync(string username, string password)
        {
            try
            {
                ETLogManager.Info($"开始登录OA系统，用户名: {username}");
                _currentUsername = username;

                _loginBrowser = new ETOALoginBrowser($"{BaseUrl}/login");

                // 尝试自动登录
                var autoLoginSuccess = await _loginBrowser.AutoLoginAsync(username, password);
                if (autoLoginSuccess)
                {
                    LoginInfo = _loginBrowser.GetLoginInfo();
                }
                else
                {
                    // 自动登录失败，显示登录对话框
                    LoginInfo = await _loginBrowser.ShowLoginDialogAsync();
                }

                if (LoginInfo != null && LoginInfo.IsSuccess)
                {
                    // 设置API客户端认证信息
                    if (IsApiClientAvailable)
                    {
                        _apiClient.SetAuthenticationInfo(LoginInfo);
                        IsLoggedIn = true;
                        ETLogManager.Info("API客户端认证信息设置成功");
                    }
                    else
                    {
                        ETLogManager.Warning("API客户端不可用，无法设置认证信息");
                        return false;
                    }

                    // 初始化会话管理（可选功能）
                    if (IsSessionManagerAvailable)
                    {
                        try
                        {
                            _sessionManager.InitializeSession(username, password);
                            await _sessionManager.StartSessionMonitoringAsync();
                            ETLogManager.Info("会话管理器启动成功");
                        }
                        catch (Exception ex)
                        {
                            ETLogManager.Warning("会话管理器启动失败，但不影响基本功能", ex);
                        }
                    }
                    else
                    {
                        ETLogManager.Warning("会话管理器不可用，会话监控功能将不可用");
                    }

                    // 保存登录配置
                    try
                    {
                        SaveLoginConfig(username);
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Warning("保存登录配置失败", ex);
                    }

                    ETLogManager.Info("OA系统登录成功");
                    return true;
                }

                ETLogManager.Warning("OA系统登录失败");
                return false;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("OA系统登录异常", ex);
                throw new ETException($"登录失败: {ex.Message}", "LoginAsync", ex);
            }
        }

        /// <summary>
        /// 获取API数据
        /// </summary>
        /// <typeparam name="T">返回数据类型</typeparam>
        /// <param name="endpoint">API端点</param>
        /// <returns>API响应数据</returns>
        public async Task<T> GetApiDataAsync<T>(string endpoint)
        {
            if (!IsLoggedIn)
                throw new InvalidOperationException("请先登录系统");

            if (!IsApiClientAvailable)
                throw new InvalidOperationException("API客户端不可用，无法执行API请求");

            return await _apiClient.GetAsync<T>(endpoint);
        }

        /// <summary>
        /// 提交API数据
        /// </summary>
        /// <typeparam name="T">返回数据类型</typeparam>
        /// <param name="endpoint">API端点</param>
        /// <param name="data">提交的数据</param>
        /// <returns>API响应数据</returns>
        public async Task<T> PostApiDataAsync<T>(string endpoint, object data)
        {
            if (!IsLoggedIn)
                throw new InvalidOperationException("请先登录系统");

            if (!IsApiClientAvailable)
                throw new InvalidOperationException("API客户端不可用，无法执行API请求");

            return await _apiClient.PostAsync<T>(endpoint, data);
        }

        /// <summary>
        /// 上传文件
        /// </summary>
        /// <param name="endpoint">上传端点</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="formData">表单数据</param>
        /// <returns>上传结果</returns>
        public async Task<ETOAUploadResult> UploadFileAsync(string endpoint, string filePath, object formData = null)
        {
            if (!IsLoggedIn)
                throw new InvalidOperationException("请先登录系统");

            if (!IsFileUploaderAvailable)
                throw new InvalidOperationException("文件上传器不可用，无法执行文件上传操作");

            return await _fileUploader.UploadFileAsync(endpoint, filePath, formData);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        /// <param name="disposing">是否正在释放</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                // 取消全局异常处理器注册
                lock (_registrationLock)
                {
                    if (_globalExceptionHandlerRegistered)
                    {
                        try
                        {
                            AppDomain.CurrentDomain.UnhandledException -= OnUnhandledException;
                            _globalExceptionHandlerRegistered = false;
                            ETLogManager.Info("全局异常处理器已取消注册");
                        }
                        catch (Exception ex)
                        {
                            ETLogManager.Error("取消全局异常处理器注册失败", ex);
                        }
                    }
                }

                // 释放会话管理器
                if (_sessionManager != null)
                {
                    _sessionManager.StopSessionMonitoring();
                    ETLogManager.Info("会话管理器已停止");
                }

                // 释放API客户端
                if (_apiClient != null)
                {
                    _apiClient.Dispose();
                    _apiClient = null;
                    ETLogManager.Info("API客户端已释放");
                }

                // 释放文件上传器
                if (_fileUploader != null)
                {
                    _fileUploader.Dispose();
                    _fileUploader = null;
                    ETLogManager.Info("文件上传器已释放");
                }

                // 关闭浏览器窗体
                _loginBrowser?.Close();
                _simulationBrowser?.Close();

                _disposed = true;
                ETLogManager.Info("ETOAClient资源释放完成");
            }
        }

        /// <summary>
        /// 保存登录配置
        /// </summary>
        /// <param name="username">用户名</param>
        private void SaveLoginConfig(string username)
        {
            try
            {
                _configFile.IniWriteValue("Login", "LastUsername", username);
                _configFile.IniWriteValue("Login", "LastLoginTime", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                _configFile.IniWriteValue("Login", "BaseUrl", BaseUrl);
                ETLogManager.Debug("登录配置保存成功");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("保存登录配置失败", ex);
            }
        }

        /// <summary>
        /// 获取配置值
        /// </summary>
        /// <param name="section">配置节</param>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        public string GetConfig(string section, string key, string defaultValue = "")
        {
            try
            {
                return _configFile.IniReadValue(section, key, defaultValue);
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"读取配置失败: {section}.{key}", ex);
                return defaultValue;
            }
        }

        /// <summary>
        /// 设置配置值
        /// </summary>
        /// <param name="section">配置节</param>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        public void SetConfig(string section, string key, string value)
        {
            try
            {
                _configFile.IniWriteValue(section, key, value);
                ETLogManager.Debug($"配置设置成功: {section}.{key} = {value}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"设置配置失败: {section}.{key}", ex);
            }
        }

        /// <summary>
        /// 获取全局设置
        /// </summary>
        /// <param name="key">设置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>设置值</returns>
        public T GetGlobalSetting<T>(string key, T defaultValue = default(T))
        {
            try
            {
                lock (_lockObject)
                {
                    if (_globalSettings.ContainsKey(key))
                    {
                        return (T)_globalSettings[key];
                    }
                }
                return defaultValue;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"获取全局设置失败: {key}", ex);
                return defaultValue;
            }
        }

        /// <summary>
        /// 设置全局设置
        /// </summary>
        /// <param name="key">设置键</param>
        /// <param name="value">设置值</param>
        public void SetGlobalSetting(string key, object value)
        {
            try
            {
                lock (_lockObject)
                {
                    _globalSettings[key] = value;
                }
                ETLogManager.Debug($"设置全局设置: {key} = {value}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"设置全局设置失败: {key}", ex);
            }
        }

        /// <summary>
        /// 获取性能统计信息
        /// </summary>
        /// <returns>性能统计信息</returns>
        public Dictionary<string, ETOAPerformanceHelper.PerformanceStats> GetPerformanceStatistics()
        {
            try
            {
                return ETOAPerformanceHelper.GetAllStats();
            }
            catch (Exception ex)
            {
                ETLogManager.Error("获取性能统计信息失败", ex);
                HandleGlobalException(ex, "GetPerformanceStatistics");
                return new Dictionary<string, ETOAPerformanceHelper.PerformanceStats>();
            }
        }

        /// <summary>
        /// 获取会话统计信息
        /// </summary>
        /// <returns>会话统计信息</returns>
        public SessionStatistics GetSessionStatistics()
        {
            try
            {
                return _sessionManager?.GetSessionStatistics() ?? new SessionStatistics();
            }
            catch (Exception ex)
            {
                ETLogManager.Error("获取会话统计信息失败", ex);
                HandleGlobalException(ex, "GetSessionStatistics");
                return new SessionStatistics();
            }
        }

        /// <summary>
        /// 创建模拟操作浏览器
        /// </summary>
        /// <param name="targetUrl">目标URL</param>
        /// <returns>模拟操作浏览器实例</returns>
        public ETOASimulationBrowser CreateSimulationBrowser(string targetUrl = null)
        {
            try
            {
                var url = targetUrl ?? BaseUrl;
                _simulationBrowser = new ETOASimulationBrowser(url);

                // 如果已登录，设置认证信息
                if (IsLoggedIn && LoginInfo != null)
                {
                    _simulationBrowser.SetAuthenticationInfo(LoginInfo);
                }

                ETLogManager.Info($"创建模拟操作浏览器: {url}");
                return _simulationBrowser;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("创建模拟操作浏览器失败", ex);
                HandleGlobalException(ex, "CreateSimulationBrowser");
                throw;
            }
        }

        /// <summary>
        /// 执行健康检查
        /// </summary>
        /// <returns>健康检查结果</returns>
        public async Task<HealthCheckResult> PerformHealthCheckAsync()
        {
            var result = new HealthCheckResult
            {
                Timestamp = DateTime.Now,
                IsHealthy = true,
                Details = new Dictionary<string, object>()
            };

            try
            {
                // 检查API客户端
                if (_apiClient != null)
                {
                    result.Details["ApiClient"] = _apiClient.IsAuthenticated ? "已认证" : "未认证";
                }
                else
                {
                    result.IsHealthy = false;
                    result.Details["ApiClient"] = "未初始化";
                }

                // 检查会话管理器
                if (_sessionManager != null)
                {
                    var sessionStats = _sessionManager.GetSessionStatistics();
                    result.Details["SessionManager"] = new
                    {
                        IsMonitoring = sessionStats.IsMonitoring,
                        IsActive = sessionStats.IsActive,
                        LastHeartbeat = sessionStats.LastHeartbeat
                    };
                }
                else
                {
                    result.IsHealthy = false;
                    result.Details["SessionManager"] = "未初始化";
                }

                // 检查性能状态
                try
                {
                    var allStats = ETOAPerformanceHelper.GetAllStats();
                    result.Details["Performance"] = new
                    {
                        TotalOperations = allStats.Count,
                        IsHealthy = true // 简化的健康检查
                    };
                }
                catch (Exception ex)
                {
                    result.Details["Performance"] = $"性能检查失败: {ex.Message}";
                    result.IsHealthy = false;
                }

                // 检查网络连接
                try
                {
                    var response = await _apiClient.GetAsync<object>("/api/health");
                    result.Details["NetworkConnection"] = response != null ? "正常" : "异常";
                    if (response == null)
                    {
                        result.IsHealthy = false;
                    }
                }
                catch (Exception ex)
                {
                    result.IsHealthy = false;
                    result.Details["NetworkConnection"] = $"异常: {ex.Message}";
                }

                ETLogManager.Info($"健康检查完成，状态: {(result.IsHealthy ? "健康" : "异常")}");
                return result;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("执行健康检查失败", ex);
                HandleGlobalException(ex, "PerformHealthCheckAsync");

                result.IsHealthy = false;
                result.Details["Error"] = ex.Message;
                return result;
            }
        }

        /// <summary>
        /// 清理资源并重置状态
        /// </summary>
        public void Reset()
        {
            try
            {
                ETLogManager.Info("开始重置ETOAClient状态");

                // 停止会话监控
                _sessionManager?.StopSessionMonitoring();

                // 清理登录状态
                IsLoggedIn = false;
                LoginInfo = null;
                _currentUsername = null;

                // 清理浏览器实例
                _loginBrowser?.Dispose();
                _loginBrowser = null;

                _simulationBrowser?.Dispose();
                _simulationBrowser = null;

                // 清理全局设置
                lock (_lockObject)
                {
                    _globalSettings.Clear();
                }

                ETLogManager.Info("ETOAClient状态重置完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("重置ETOAClient状态失败", ex);
                HandleGlobalException(ex, "Reset");
            }
        }

        #endregion 公共方法
    }

    #region 事件参数类和结果类

    /// <summary>
    /// 全局异常事件参数
    /// </summary>
    public class GlobalExceptionEventArgs : EventArgs
    {
        /// <summary>
        /// 异常信息
        /// </summary>
        public Exception Exception { get; set; }

        /// <summary>
        /// 异常来源
        /// </summary>
        public string Source { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 是否已处理
        /// </summary>
        public bool Handled { get; set; }
    }

    /// <summary>
    /// 登录状态变更事件参数
    /// </summary>
    public class LoginStatusChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 是否已登录
        /// </summary>
        public bool IsLoggedIn { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// 性能事件参数
    /// </summary>
    public class PerformanceEventArgs : EventArgs
    {
        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 性能指标
        /// </summary>
        public ETOAPerformanceHelper.PerformanceMetric Metrics { get; set; }

        /// <summary>
        /// 严重级别
        /// </summary>
        public PerformanceSeverity Severity { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 健康检查结果
    /// </summary>
    public class HealthCheckResult
    {
        /// <summary>
        /// 是否健康
        /// </summary>
        public bool IsHealthy { get; set; }

        /// <summary>
        /// 检查时间
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 详细信息
        /// </summary>
        public Dictionary<string, object> Details { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// 性能严重级别
    /// </summary>
    public enum PerformanceSeverity
    {
        /// <summary>
        /// 信息
        /// </summary>
        Info,

        /// <summary>
        /// 警告
        /// </summary>
        Warning,

        /// <summary>
        /// 错误
        /// </summary>
        Error,

        /// <summary>
        /// 严重
        /// </summary>
        Critical
    }

    #endregion 事件参数类和结果类
}