using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using CefSharp;
using CefSharp.WinForms;
using ET;
using ET.ETOAAutomation.Models;

namespace ET.ETOAAutomation
{
    /// <summary>
    /// 模拟操作浏览器窗体，提供网页自动化操作功能 窗体文件：包含ETOASimulationBrowser.cs、ETOASimulationBrowser.Designer.cs、ETOASimulationBrowser.resx
    /// </summary>
    public partial class ETOASimulationBrowser : Form
    {
        #region Windows API声明

        [DllImport("user32.dll", SetLastError = true)]
        private static extern bool SetCursorPos(int x, int y);

        [DllImport("user32.dll", SetLastError = true)]
        private static extern void mouse_event(uint dwFlags, uint dx, uint dy, uint dwData, int dwExtraInfo);

        [DllImport("user32.dll", SetLastError = true)]
        private static extern void keybd_event(byte bVk, byte bScan, uint dwFlags, int dwExtraInfo);

        [DllImport("user32.dll", SetLastError = true)]
        private static extern short VkKeyScan(char ch);

        [DllImport("user32.dll", SetLastError = true)]
        private static extern uint MapVirtualKey(uint uCode, uint uMapType);

        private const uint MOUSEEVENTF_LEFTDOWN = 0x02;
        private const uint MOUSEEVENTF_LEFTUP = 0x04;
        private const uint MOUSEEVENTF_RIGHTDOWN = 0x08;
        private const uint MOUSEEVENTF_RIGHTUP = 0x10;
        private const uint KEYEVENTF_KEYUP = 0x02;

        #endregion Windows API声明

        #region 私有字段

        private ChromiumWebBrowser _browser;
        private bool _isPageLoaded = false;
        private System.Windows.Forms.Timer _statusTimer;
        private Dictionary<string, string> _lastCookies;
        private string _lastUserInfo;
        private CancellationTokenSource _operationCancellation;

        #endregion 私有字段

        #region 公共属性

        /// <summary>
        /// 当前URL
        /// </summary>
        public string CurrentUrl => _browser?.Address ?? string.Empty;

        /// <summary>
        /// 页面是否加载完成
        /// </summary>
        public bool IsPageLoaded => _isPageLoaded;

        /// <summary>
        /// CefSharp浏览器控件
        /// </summary>
        public ChromiumWebBrowser Browser => _browser;

        /// <summary>
        /// URL输入框
        /// </summary>
        public TextBox TxtUrl { get; private set; }

        /// <summary>
        /// 导航按钮
        /// </summary>
        public Button BtnNavigate { get; private set; }

        /// <summary>
        /// 后退按钮
        /// </summary>
        public Button BtnBack { get; private set; }

        /// <summary>
        /// 前进按钮
        /// </summary>
        public Button BtnForward { get; private set; }

        /// <summary>
        /// 刷新按钮
        /// </summary>
        public Button BtnRefresh { get; private set; }

        /// <summary>
        /// 状态标签
        /// </summary>
        public Label LblStatus { get; private set; }

        /// <summary>
        /// 进度条
        /// </summary>
        public ProgressBar ProgressBar { get; private set; }

        /// <summary>
        /// 控制面板
        /// </summary>
        public Panel PanelControls { get; private set; }

        /// <summary>
        /// 菜单栏
        /// </summary>
        public MenuStrip MenuStrip { get; private set; }

        #endregion 公共属性

        #region 事件定义

        /// <summary>
        /// 页面加载完成事件
        /// </summary>
        public event EventHandler PageLoadCompleted;

        /// <summary>
        /// API数据获取完成事件
        /// </summary>
        public event EventHandler<string> ApiDataReceived;

        /// <summary>
        /// 操作错误事件
        /// </summary>
        public event EventHandler<Exception> OperationError;

        #endregion 事件定义

        #region 构造函数

        /// <summary>
        /// 初始化模拟操作浏览器
        /// </summary>
        /// <param name="url">初始URL</param>
        public ETOASimulationBrowser(string url = "")
        {
            InitializeComponent();
            InitializeBrowser(url);
            InitializeTimer();
            InitializeEventHandlers();

            _lastCookies = new Dictionary<string, string>();
            _operationCancellation = new CancellationTokenSource();

            ETLogManager.Info("ETOASimulationBrowser", "模拟操作浏览器初始化完成");
        }

        #endregion 构造函数

        #region 私有方法

        /// <summary>
        /// 初始化浏览器
        /// </summary>
        /// <param name="url">初始URL</param>
        private void InitializeBrowser(string url)
        {
            _browser = new ChromiumWebBrowser(string.IsNullOrEmpty(url) ? "about:blank" : url)
            {
                Dock = DockStyle.Fill
            };

            // 添加浏览器到主面板
            var browserPanel = this.Controls.Find("panelBrowser", true)[0] as Panel;
            browserPanel?.Controls.Add(_browser);

            // 绑定事件
            _browser.LoadingStateChanged += Browser_LoadingStateChanged;
            _browser.AddressChanged += Browser_AddressChanged;
            _browser.TitleChanged += Browser_TitleChanged;
        }

        /// <summary>
        /// 浏览器加载状态改变事件
        /// </summary>
        private void Browser_LoadingStateChanged(object sender, LoadingStateChangedEventArgs e)
        {
            this.Invoke(new Action(() =>
            {
                BtnBack.Enabled = _browser.CanGoBack;
                BtnForward.Enabled = _browser.CanGoForward;

                if (!e.IsLoading)
                {
                    _isPageLoaded = true;
                    LblStatus.Text = "页面加载完成";
                    ProgressBar.Value = 100;
                    PageLoadCompleted?.Invoke(this, EventArgs.Empty);
                }
                else
                {
                    _isPageLoaded = false;
                    LblStatus.Text = "正在加载页面...";
                    ProgressBar.Value = 50;
                }
            }));
        }

        /// <summary>
        /// 浏览器地址改变事件
        /// </summary>
        private void Browser_AddressChanged(object sender, AddressChangedEventArgs e)
        {
            this.Invoke(new Action(() =>
            {
                TxtUrl.Text = e.Address;
                LblStatus.Text = $"导航到: {e.Address}";
            }));
        }

        /// <summary>
        /// 浏览器标题改变事件
        /// </summary>
        private void Browser_TitleChanged(object sender, TitleChangedEventArgs e)
        {
            this.Invoke(new Action(() =>
            {
                this.Text = $"模拟操作浏览器 - {e.Title}";
            }));
        }

        /// <summary>
        /// 初始化定时器
        /// </summary>
        private void InitializeTimer()
        {
            _statusTimer = new System.Windows.Forms.Timer();
            _statusTimer.Interval = 5000; // 5秒检查一次
            _statusTimer.Tick += StatusTimer_Tick;
            _statusTimer.Start();
        }

        /// <summary>
        /// 初始化事件处理器
        /// </summary>
        private void InitializeEventHandlers()
        {
            this.FormClosing += ETOASimulationBrowser_FormClosing;
            this.Load += ETOASimulationBrowser_Load;
        }

        /// <summary>
        /// 窗体加载事件
        /// </summary>
        private void ETOASimulationBrowser_Load(object sender, EventArgs e)
        {
            // 设置窗体初始状态
            LblStatus.Text = "浏览器已就绪";
            ProgressBar.Value = 0;
        }

        /// <summary>
        /// 窗体关闭事件
        /// </summary>
        private void ETOASimulationBrowser_FormClosing(object sender, FormClosingEventArgs e)
        {
            _statusTimer?.Stop();
            _statusTimer?.Dispose();
            _operationCancellation?.Cancel();
            _operationCancellation?.Dispose();
            _browser?.Dispose();
        }

        /// <summary>
        /// 状态定时器事件
        /// </summary>
        private async void StatusTimer_Tick(object sender, EventArgs e)
        {
            try
            {
                if (_isPageLoaded && !_browser.IsLoading)
                {
                    await CheckLoginStatusAsync();
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOASimulationBrowser", $"状态检查失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查登录状态（异步版本）
        /// </summary>
        private async Task CheckLoginStatusAsync()
        {
            try
            {
                var cookies = await GetAllCookiesAsync();
                var userInfo = await GetUserInfoFromPageAsync();

                // 检查是否有登录相关的变化
                if (HasLoginInfoChanged(cookies, userInfo))
                {
                    _lastCookies = cookies;
                    _lastUserInfo = userInfo.ContainsKey("username") ? userInfo["username"] : "";

                    await ExtractLoginInfoAsync();
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOASimulationBrowser", $"检查登录状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 提取登录信息（异步版本）
        /// </summary>
        private async Task ExtractLoginInfoAsync()
        {
            try
            {
                var loginData = new StringBuilder();
                loginData.AppendLine($"URL: {CurrentUrl}");
                loginData.AppendLine($"时间: {DateTime.Now}");

                // 获取Cookie信息
                var cookies = await GetAllCookiesAsync();
                if (cookies.Count > 0)
                {
                    loginData.AppendLine("Cookies:");
                    foreach (var cookie in cookies)
                    {
                        loginData.AppendLine($"  {cookie.Key}: {cookie.Value}");
                    }
                }

                // 获取用户信息
                var userInfo = await GetUserInfoFromPageAsync();
                if (userInfo.Count > 0)
                {
                    loginData.AppendLine("用户信息:");
                    foreach (var info in userInfo)
                    {
                        loginData.AppendLine($"  {info.Key}: {info.Value}");
                    }
                }

                // 触发API数据获取完成事件
                ApiDataReceived?.Invoke(this, loginData.ToString());

                ETLogManager.Info("ETOASimulationBrowser", "登录信息提取完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOASimulationBrowser", $"提取登录信息失败: {ex.Message}");
                OperationError?.Invoke(this, ex);
            }
        }

        /// <summary>
        /// 获取所有Cookie
        /// </summary>
        private async Task<Dictionary<string, string>> GetAllCookiesAsync()
        {
            var cookies = new Dictionary<string, string>();

            try
            {
                var script = @"
                    var cookies = {};
                    document.cookie.split(';').forEach(function(cookie) {
                        var parts = cookie.trim().split('=');
                        if (parts.length === 2) {
                            cookies[parts[0]] = parts[1];
                        }
                    });
                    return JSON.stringify(cookies);
                ";

                var result = await _browser.EvaluateScriptAsync(script);
                if (result.Success && result.Result != null)
                {
                    var cookieJson = result.Result.ToString();
                    // 简单解析JSON（这里可以使用更完善的JSON解析） 为了避免依赖，使用简单的字符串解析
                    var cookieData = ParseSimpleJson(cookieJson);
                    return cookieData;
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOASimulationBrowser", $"获取Cookie失败: {ex.Message}");
            }

            return cookies;
        }

        /// <summary>
        /// 从页面获取用户信息
        /// </summary>
        private async Task<Dictionary<string, string>> GetUserInfoFromPageAsync()
        {
            var userInfo = new Dictionary<string, string>();

            try
            {
                // 尝试从常见的用户信息元素获取数据
                var selectors = new[]
                {
                    "input[name='username']", "input[name='user']", "input[name='email']",
                    ".username", ".user-name", ".user-info", "#username", "#user"
                };

                foreach (var selector in selectors)
                {
                    var script = $@"
                        var element = document.querySelector('{selector}');
                        if (element) {{
                            return element.value || element.textContent || element.innerText || '';
                        }}
                        return '';
                    ";

                    var result = await _browser.EvaluateScriptAsync(script);
                    if (result.Success && !string.IsNullOrEmpty(result.Result?.ToString()))
                    {
                        userInfo[selector] = result.Result.ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOASimulationBrowser", $"获取用户信息失败: {ex.Message}");
            }

            return userInfo;
        }

        /// <summary>
        /// 检查登录信息是否有变化
        /// </summary>
        private bool HasLoginInfoChanged(Dictionary<string, string> cookies, Dictionary<string, string> userInfo)
        {
            // 检查Cookie是否有变化
            if (cookies.Count != _lastCookies.Count)
                return true;

            foreach (var cookie in cookies)
            {
                if (!_lastCookies.ContainsKey(cookie.Key) || _lastCookies[cookie.Key] != cookie.Value)
                    return true;
            }

            // 检查用户信息是否有变化
            var currentUserInfo = userInfo.ContainsKey("username") ? userInfo["username"] : "";
            if (currentUserInfo != _lastUserInfo)
                return true;

            return false;
        }

        /// <summary>
        /// 简单的JSON解析（仅用于Cookie解析）
        /// </summary>
        private Dictionary<string, string> ParseSimpleJson(string json)
        {
            var result = new Dictionary<string, string>();

            try
            {
                json = json.Trim('{', '}', '"');
                var pairs = json.Split(',');

                foreach (var pair in pairs)
                {
                    var keyValue = pair.Split(':');
                    if (keyValue.Length == 2)
                    {
                        var key = keyValue[0].Trim('"', ' ');
                        var value = keyValue[1].Trim('"', ' ');
                        result[key] = value;
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOASimulationBrowser", $"JSON解析失败: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 尝试自动填写登录表单
        /// </summary>
        private async Task TryAutoFillLoginForm()
        {
            try
            {
                // 这里可以实现自动填写登录表单的逻辑 根据具体需求实现
                await Task.Delay(100); // 占位符
                ETLogManager.Info("ETOASimulationBrowser", "自动填写登录表单功能待实现");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOASimulationBrowser", $"自动填写登录表单失败: {ex.Message}");
            }
        }

        #endregion 私有方法

        #region 公共方法

        /// <summary>
        /// 导航到指定URL
        /// </summary>
        /// <param name="url">目标URL</param>
        public async Task NavigateAsync(string url)
        {
            try
            {
                _browser.Load(url);

                // 等待页面加载完成
                while (_browser.IsLoading)
                {
                    await Task.Delay(100);
                }
            }
            catch (Exception ex)
            {
                OperationError?.Invoke(this, ex);
            }
        }

        /// <summary>
        /// 填写文本框（DOM操作方式）
        /// </summary>
        /// <param name="selector">CSS选择器</param>
        /// <param name="text">文本内容</param>
        /// <returns>操作是否成功</returns>
        public async Task<bool> FillTextAsync(string selector, string text)
        {
            try
            {
                var script = $@"
                    var element = document.querySelector('{selector}');
                    if (element) {{
                        element.value = '{text}';
                        element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                        element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                        return true;
                    }}
                    return false;
                ";

                var result = await _browser.EvaluateScriptAsync(script);
                return result.Success && (bool)result.Result;
            }
            catch (Exception ex)
            {
                OperationError?.Invoke(this, ex);
                return false;
            }
        }

        /// <summary>
        /// 点击元素（DOM操作方式）
        /// </summary>
        /// <param name="selector">CSS选择器</param>
        /// <returns>操作是否成功</returns>
        public async Task<bool> ClickElementAsync(string selector)
        {
            try
            {
                var script = $@"
                    var element = document.querySelector('{selector}');
                    if (element) {{
                        element.click();
                        return true;
                    }}
                    return false;
                ";

                var result = await _browser.EvaluateScriptAsync(script);
                return result.Success && (bool)result.Result;
            }
            catch (Exception ex)
            {
                OperationError?.Invoke(this, ex);
                return false;
            }
        }

        /// <summary>
        /// 选择下拉框选项（DOM操作方式）
        /// </summary>
        /// <param name="selector">CSS选择器</param>
        /// <param name="value">选项值</param>
        /// <returns>操作是否成功</returns>
        public async Task<bool> SelectOptionAsync(string selector, string value)
        {
            try
            {
                var script = $@"
                    var element = document.querySelector('{selector}');
                    if (element && element.tagName === 'SELECT') {{
                        element.value = '{value}';
                        element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                        return true;
                    }}
                    return false;
                ";

                var result = await _browser.EvaluateScriptAsync(script);
                return result.Success && (bool)result.Result;
            }
            catch (Exception ex)
            {
                OperationError?.Invoke(this, ex);
                return false;
            }
        }

        /// <summary>
        /// 在指定坐标点击（JavaScript方式）
        /// </summary>
        /// <param name="x">X坐标</param>
        /// <param name="y">Y坐标</param>
        /// <returns>操作是否成功</returns>
        public async Task<bool> ClickAtAsync(int x, int y)
        {
            try
            {
                var script = $@"
                    var event = new MouseEvent('click', {{
                        view: window,
                        bubbles: true,
                        cancelable: true,
                        clientX: {x},
                        clientY: {y}
                    }});
                    var element = document.elementFromPoint({x}, {y});
                    if (element) {{
                        element.dispatchEvent(event);
                        return true;
                    }}
                    return false;
                ";

                var result = await _browser.EvaluateScriptAsync(script);
                bool jsSuccess = result.Success && (bool)result.Result;

                if (!jsSuccess)
                {
                    ETLogManager.Warning("ETOASimulationBrowser", "JavaScript点击失败，尝试使用Windows API");
                    // 降级到Windows API方式
                    return ClickAtWithApi(x, y);
                }

                return jsSuccess;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOASimulationBrowser", $"坐标点击失败: {ex.Message}");
                OperationError?.Invoke(this, ex);

                // 异常时尝试Windows API方式
                try
                {
                    return ClickAtWithApi(x, y);
                }
                catch (Exception apiEx)
                {
                    ETLogManager.Error("ETOASimulationBrowser", $"Windows API点击也失败: {apiEx.Message}");
                    return false;
                }
            }
        }

        /// <summary>
        /// 使用Windows API在指定坐标点击
        /// </summary>
        /// <param name="x">X坐标</param>
        /// <param name="y">Y坐标</param>
        /// <returns>操作是否成功</returns>
        public bool ClickAtWithApi(int x, int y)
        {
            try
            {
                // 转换为屏幕坐标
                var screenPoint = _browser.PointToScreen(new Point(x, y));

                // 使用Windows API进行点击
                bool success = SimulateMouseClick(screenPoint.X, screenPoint.Y);

                if (success)
                {
                    ETLogManager.Debug("ETOASimulationBrowser", $"Windows API点击成功: ({x}, {y}) -> 屏幕坐标({screenPoint.X}, {screenPoint.Y})");
                }

                return success;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOASimulationBrowser", $"Windows API点击失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 发送键盘输入（坐标操作方式）
        /// </summary>
        /// <param name="keys">按键内容</param>
        /// <returns>操作是否成功</returns>
        public async Task<bool> SendKeysAsync(string keys)
        {
            try
            {
                if (string.IsNullOrEmpty(keys))
                {
                    ETLogManager.Warning("ETOASimulationBrowser", "发送的按键内容为空");
                    return true;
                }

                int successCount = 0;
                int totalCount = keys.Length;

                foreach (char c in keys)
                {
                    bool keySuccess = await SendSingleKeyAsync(c);
                    if (keySuccess)
                    {
                        successCount++;
                    }
                    else
                    {
                        ETLogManager.Warning("ETOASimulationBrowser", $"发送字符 '{c}' 失败");
                    }

                    await Task.Delay(50); // 按键间隔
                }

                bool overallSuccess = successCount == totalCount;
                if (!overallSuccess)
                {
                    ETLogManager.Warning("ETOASimulationBrowser", $"按键发送部分失败: {successCount}/{totalCount}");
                }

                return overallSuccess;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOASimulationBrowser", $"发送键盘输入失败: {ex.Message}");
                OperationError?.Invoke(this, ex);
                return false;
            }
        }

        /// <summary>
        /// 等待元素出现
        /// </summary>
        /// <param name="selector">CSS选择器</param>
        /// <param name="timeoutMs">超时时间（毫秒）</param>
        /// <returns>元素是否出现</returns>
        public async Task<bool> WaitForElementAsync(string selector, int timeoutMs = 10000)
        {
            try
            {
                var startTime = DateTime.Now;

                while ((DateTime.Now - startTime).TotalMilliseconds < timeoutMs)
                {
                    var script = $"document.querySelector('{selector}') !== null";
                    var result = await _browser.EvaluateScriptAsync(script);

                    if (result.Success && (bool)result.Result)
                    {
                        return true;
                    }

                    await Task.Delay(100);
                }

                return false;
            }
            catch (Exception ex)
            {
                OperationError?.Invoke(this, ex);
                return false;
            }
        }

        /// <summary>
        /// 获取页面HTML内容
        /// </summary>
        /// <returns>页面HTML</returns>
        public async Task<string> GetPageHtmlAsync()
        {
            try
            {
                var result = await _browser.EvaluateScriptAsync("document.documentElement.outerHTML");
                return result.Success ? result.Result?.ToString() ?? "" : "";
            }
            catch (Exception ex)
            {
                OperationError?.Invoke(this, ex);
                return "";
            }
        }

        /// <summary>
        /// 获取元素文本内容
        /// </summary>
        /// <param name="selector">CSS选择器</param>
        /// <returns>元素文本</returns>
        public async Task<string> GetElementTextAsync(string selector)
        {
            try
            {
                var script = $@"
                    var element = document.querySelector('{selector}');
                    if (element) {{
                        return element.textContent || element.innerText || '';
                    }}
                    return '';
                ";

                var result = await _browser.EvaluateScriptAsync(script);
                return result.Success ? result.Result?.ToString() ?? "" : "";
            }
            catch (Exception ex)
            {
                OperationError?.Invoke(this, ex);
                return "";
            }
        }

        /// <summary>
        /// 获取元素属性值
        /// </summary>
        /// <param name="selector">CSS选择器</param>
        /// <param name="attribute">属性名</param>
        /// <returns>属性值</returns>
        public async Task<string> GetElementAttributeAsync(string selector, string attribute)
        {
            try
            {
                var script = $@"
                    var element = document.querySelector('{selector}');
                    if (element) {{
                        return element.getAttribute('{attribute}') || '';
                    }}
                    return '';
                ";

                var result = await _browser.EvaluateScriptAsync(script);
                return result.Success ? result.Result?.ToString() ?? "" : "";
            }
            catch (Exception ex)
            {
                OperationError?.Invoke(this, ex);
                return "";
            }
        }

        /// <summary>
        /// 执行JavaScript代码
        /// </summary>
        /// <param name="script">JavaScript代码</param>
        /// <returns>执行结果</returns>
        public async Task<object> ExecuteScriptAsync(string script)
        {
            try
            {
                var result = await _browser.EvaluateScriptAsync(script);
                return result.Success ? result.Result : null;
            }
            catch (Exception ex)
            {
                OperationError?.Invoke(this, ex);
                return null;
            }
        }

        /// <summary>
        /// 截取页面屏幕截图
        /// </summary>
        /// <returns>截图的Bitmap对象</returns>
        public async Task<Bitmap> TakeScreenshotAsync()
        {
            try
            {
                var tcs = new TaskCompletionSource<Bitmap>();

                this.Invoke(new Action(() =>
                {
                    try
                    {
                        var bitmap = new Bitmap(_browser.Width, _browser.Height);
                        _browser.DrawToBitmap(bitmap, new Rectangle(0, 0, _browser.Width, _browser.Height));
                        tcs.SetResult(bitmap);
                    }
                    catch (Exception ex)
                    {
                        tcs.SetException(ex);
                    }
                }));

                return await tcs.Task;
            }
            catch (Exception ex)
            {
                OperationError?.Invoke(this, ex);
                return null;
            }
        }

        /// <summary>
        /// 保存页面截图到文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否保存成功</returns>
        public async Task<bool> SaveScreenshotAsync(string filePath)
        {
            try
            {
                var bitmap = await TakeScreenshotAsync();
                if (bitmap != null)
                {
                    bitmap.Save(filePath);
                    bitmap.Dispose();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                OperationError?.Invoke(this, ex);
                return false;
            }
        }

        /// <summary>
        /// 滚动页面
        /// </summary>
        /// <param name="x">水平滚动距离</param>
        /// <param name="y">垂直滚动距离</param>
        /// <returns>操作是否成功</returns>
        public async Task<bool> ScrollPageAsync(int x, int y)
        {
            try
            {
                var script = $"window.scrollBy({x}, {y});";
                var result = await _browser.EvaluateScriptAsync(script);
                return result.Success;
            }
            catch (Exception ex)
            {
                OperationError?.Invoke(this, ex);
                return false;
            }
        }

        /// <summary>
        /// 滚动到页面顶部
        /// </summary>
        /// <returns>操作是否成功</returns>
        public async Task<bool> ScrollToTopAsync()
        {
            try
            {
                var script = "window.scrollTo(0, 0);";
                var result = await _browser.EvaluateScriptAsync(script);
                return result.Success;
            }
            catch (Exception ex)
            {
                OperationError?.Invoke(this, ex);
                return false;
            }
        }

        /// <summary>
        /// 滚动到页面底部
        /// </summary>
        /// <returns>操作是否成功</returns>
        public async Task<bool> ScrollToBottomAsync()
        {
            try
            {
                var script = "window.scrollTo(0, document.body.scrollHeight);";
                var result = await _browser.EvaluateScriptAsync(script);
                return result.Success;
            }
            catch (Exception ex)
            {
                OperationError?.Invoke(this, ex);
                return false;
            }
        }

        /// <summary>
        /// 设置认证信息
        /// </summary>
        /// <param name="loginInfo">登录信息</param>
        public void SetAuthenticationInfo(ETOALoginInfo loginInfo)
        {
            try
            {
                if (loginInfo != null && loginInfo.IsSuccess)
                {
                    // 设置Cookie信息
                    foreach (var cookie in loginInfo.Cookies)
                    {
                        _browser.GetCookieManager().SetCookie(_browser.Address, new Cookie
                        {
                            Name = cookie.Key,
                            Value = cookie.Value,
                            Domain = new Uri(_browser.Address).Host
                        });
                    }

                    ETLogManager.Info("ETOASimulationBrowser", "认证信息设置完成");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOASimulationBrowser", $"设置认证信息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 关闭浏览器
        /// </summary>
        public new void Close()
        {
            try
            {
                _statusTimer?.Stop();
                _statusTimer?.Dispose();
                _operationCancellation?.Cancel();
                _operationCancellation?.Dispose();
                _browser?.Dispose();

                ETLogManager.Info("ETOASimulationBrowser", "浏览器已关闭");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOASimulationBrowser", $"关闭浏览器时发生错误: {ex.Message}");
            }
            finally
            {
                base.Close();
            }
        }

        #endregion 公共方法

        #region 辅助方法

        /// <summary>
        /// 发送单个按键
        /// </summary>
        /// <param name="key">按键字符</param>
        /// <returns>操作是否成功</returns>
        private async Task<bool> SendSingleKeyAsync(char key)
        {
            try
            {
                // 获取虚拟键码
                short vkResult = VkKeyScan(key);
                if (vkResult == -1)
                {
                    ETLogManager.Error("ETOASimulationBrowser", $"无法获取字符 '{key}' 的虚拟键码");
                    return false;
                }

                byte vkCode = (byte)(vkResult & 0xFF);
                if (vkCode == 0)
                {
                    ETLogManager.Error("ETOASimulationBrowser", $"字符 '{key}' 的虚拟键码无效");
                    return false;
                }

                // 获取扫描码
                uint scanCode = MapVirtualKey(vkCode, 0);

                try
                {
                    // 发送按键按下事件
                    keybd_event(vkCode, (byte)scanCode, 0, 0);
                    await Task.Delay(10);

                    // 发送按键释放事件
                    keybd_event(vkCode, (byte)scanCode, KEYEVENTF_KEYUP, 0);

                    ETLogManager.Debug("ETOASimulationBrowser", $"按键发送成功: '{key}' (VK: {vkCode})");
                    return true;
                }
                catch (Exception keyEx)
                {
                    ETLogManager.Error("ETOASimulationBrowser", $"键盘事件发送失败: {keyEx.Message}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOASimulationBrowser", $"发送按键失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取字符对应的虚拟键码
        /// </summary>
        /// <param name="key">字符</param>
        /// <returns>虚拟键码</returns>
        private byte GetVirtualKeyCode(char key)
        {
            // 简化的虚拟键码映射
            if (key >= 'A' && key <= 'Z')
                return (byte)key;
            if (key >= 'a' && key <= 'z')
                return (byte)(key - 'a' + 'A');
            if (key >= '0' && key <= '9')
                return (byte)key;

            // 特殊字符映射
            switch (key)
            {
                case ' ': return 0x20; // VK_SPACE
                case '\r':
                case '\n': return 0x0D; // VK_RETURN
                case '\t': return 0x09; // VK_TAB
                case '\b': return 0x08; // VK_BACK
                default: return 0;
            }
        }

        /// <summary>
        /// 模拟鼠标点击
        /// </summary>
        /// <param name="x">X坐标</param>
        /// <param name="y">Y坐标</param>
        /// <param name="isRightClick">是否右键点击</param>
        /// <returns>操作是否成功</returns>
        private bool SimulateMouseClick(int x, int y, bool isRightClick = false)
        {
            try
            {
                // 检查坐标有效性
                if (x < 0 || y < 0)
                {
                    ETLogManager.Error("ETOASimulationBrowser", $"无效的鼠标坐标: ({x}, {y})");
                    return false;
                }

                // 设置鼠标位置
                if (!SetCursorPos(x, y))
                {
                    var errorCode = Marshal.GetLastWin32Error();
                    ETLogManager.Error("ETOASimulationBrowser", $"设置鼠标位置失败: ({x}, {y}), 错误代码: {errorCode}");
                    return false;
                }

                Thread.Sleep(10);

                // 执行鼠标点击
                try
                {
                    if (isRightClick)
                    {
                        mouse_event(MOUSEEVENTF_RIGHTDOWN, 0, 0, 0, 0);
                        Thread.Sleep(10);
                        mouse_event(MOUSEEVENTF_RIGHTUP, 0, 0, 0, 0);
                    }
                    else
                    {
                        mouse_event(MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0);
                        Thread.Sleep(10);
                        mouse_event(MOUSEEVENTF_LEFTUP, 0, 0, 0, 0);
                    }

                    ETLogManager.Debug("ETOASimulationBrowser", $"鼠标点击成功: ({x}, {y}), 右键: {isRightClick}");
                    return true;
                }
                catch (Exception mouseEx)
                {
                    ETLogManager.Error("ETOASimulationBrowser", $"鼠标事件发送失败: {mouseEx.Message}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOASimulationBrowser", $"模拟鼠标点击失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查Windows API可用性
        /// </summary>
        /// <returns>API是否可用</returns>
        private bool CheckWindowsApiAvailability()
        {
            try
            {
                // 测试SetCursorPos API
                var currentPos = Cursor.Position;
                bool testResult = SetCursorPos(currentPos.X, currentPos.Y);

                if (!testResult)
                {
                    var errorCode = Marshal.GetLastWin32Error();
                    ETLogManager.Warning("ETOASimulationBrowser", $"Windows API测试失败，错误代码: {errorCode}");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOASimulationBrowser", $"Windows API可用性检查失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取Windows API状态信息
        /// </summary>
        /// <returns>状态信息</returns>
        public string GetWindowsApiStatus()
        {
            try
            {
                bool isAvailable = CheckWindowsApiAvailability();
                return $"Windows API状态: {(isAvailable ? "可用" : "不可用")}";
            }
            catch (Exception ex)
            {
                return $"获取Windows API状态失败: {ex.Message}";
            }
        }

        #endregion 辅助方法
    }
}