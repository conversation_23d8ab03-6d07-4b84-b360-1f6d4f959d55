using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ET;

namespace ET.ETOAAutomation.Helpers
{
    /// <summary>
    /// 性能监控和优化辅助类，提供性能指标收集和分析功能
    /// </summary>
    public static class ETOAPerformanceHelper
    {
        #region 性能指标类
        /// <summary>
        /// 性能指标
        /// </summary>
        public class PerformanceMetric
        {
            public string OperationName { get; set; }
            public DateTime StartTime { get; set; }
            public DateTime EndTime { get; set; }
            public TimeSpan Duration => EndTime - StartTime;
            public bool IsSuccess { get; set; }
            public string ErrorMessage { get; set; }
            public long MemoryBefore { get; set; }
            public long MemoryAfter { get; set; }
            public long MemoryUsed => MemoryAfter - MemoryBefore;
            public Dictionary<string, object> CustomData { get; set; } = new Dictionary<string, object>();
        }

        /// <summary>
        /// 性能统计
        /// </summary>
        public class PerformanceStats
        {
            public string OperationName { get; set; }
            public int TotalCount { get; set; }
            public int SuccessCount { get; set; }
            public int FailureCount { get; set; }
            public double SuccessRate => TotalCount > 0 ? (double)SuccessCount / TotalCount * 100 : 0;
            public TimeSpan TotalDuration { get; set; }
            public TimeSpan AverageDuration => TotalCount > 0 ? TimeSpan.FromTicks(TotalDuration.Ticks / TotalCount) : TimeSpan.Zero;
            public TimeSpan MinDuration { get; set; } = TimeSpan.MaxValue;
            public TimeSpan MaxDuration { get; set; } = TimeSpan.MinValue;
            public long TotalMemoryUsed { get; set; }
            public long AverageMemoryUsed => TotalCount > 0 ? TotalMemoryUsed / TotalCount : 0;
            public DateTime FirstRecordTime { get; set; }
            public DateTime LastRecordTime { get; set; }
        }
        #endregion

        #region 私有字段
        private static readonly ConcurrentDictionary<string, PerformanceStats> _performanceStats = new ConcurrentDictionary<string, PerformanceStats>();
        private static readonly ConcurrentQueue<PerformanceMetric> _recentMetrics = new ConcurrentQueue<PerformanceMetric>();
        private static readonly int _maxRecentMetrics = 1000;
        private static readonly Timer _cleanupTimer;
        private static readonly object _cleanupLock = new object();
        #endregion

        #region 静态构造函数
        static ETOAPerformanceHelper()
        {
            // 每5分钟清理一次过期数据
            _cleanupTimer = new Timer(CleanupExpiredData, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
        }
        #endregion

        #region 性能监控方法
        /// <summary>
        /// 性能监控器
        /// </summary>
        public class PerformanceMonitor : IDisposable
        {
            private readonly string _operationName;
            private readonly Stopwatch _stopwatch;
            private readonly long _memoryBefore;
            private bool _disposed = false;

            public PerformanceMonitor(string operationName)
            {
                _operationName = operationName ?? "Unknown";
                _memoryBefore = GC.GetTotalMemory(false);
                _stopwatch = Stopwatch.StartNew();
                
                ETLogManager.Info($"开始监控操作: {_operationName}");
            }

            public void Dispose()
            {
                if (!_disposed)
                {
                    _stopwatch.Stop();
                    var memoryAfter = GC.GetTotalMemory(false);
                    
                    var metric = new PerformanceMetric
                    {
                        OperationName = _operationName,
                        StartTime = DateTime.Now - _stopwatch.Elapsed,
                        EndTime = DateTime.Now,
                        IsSuccess = true,
                        MemoryBefore = _memoryBefore,
                        MemoryAfter = memoryAfter
                    };
                    
                    RecordMetric(metric);
                    _disposed = true;
                    
                    ETLogManager.Info($"操作完成: {_operationName}, 耗时: {_stopwatch.ElapsedMilliseconds}ms, 内存使用: {metric.MemoryUsed} bytes");
                }
            }

            public void SetError(string errorMessage)
            {
                if (!_disposed)
                {
                    _stopwatch.Stop();
                    var memoryAfter = GC.GetTotalMemory(false);
                    
                    var metric = new PerformanceMetric
                    {
                        OperationName = _operationName,
                        StartTime = DateTime.Now - _stopwatch.Elapsed,
                        EndTime = DateTime.Now,
                        IsSuccess = false,
                        ErrorMessage = errorMessage,
                        MemoryBefore = _memoryBefore,
                        MemoryAfter = memoryAfter
                    };
                    
                    RecordMetric(metric);
                    _disposed = true;
                    
                    ETLogManager.Warning($"操作失败: {_operationName}, 耗时: {_stopwatch.ElapsedMilliseconds}ms, 错误: {errorMessage}");
                }
            }

            public void AddCustomData(string key, object value)
            {
                // 这里可以添加自定义数据，在Dispose时会包含在metric中
            }
        }

        /// <summary>
        /// 开始监控操作
        /// </summary>
        /// <param name="operationName">操作名称</param>
        /// <returns>性能监控器</returns>
        public static PerformanceMonitor StartMonitoring(string operationName)
        {
            return new PerformanceMonitor(operationName);
        }

        /// <summary>
        /// 监控异步操作
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="operationName">操作名称</param>
        /// <param name="operation">要监控的操作</param>
        /// <returns>操作结果</returns>
        public static async Task<T> MonitorAsync<T>(string operationName, Func<Task<T>> operation)
        {
            using (var monitor = StartMonitoring(operationName))
            {
                try
                {
                    return await operation();
                }
                catch (Exception ex)
                {
                    monitor.SetError(ex.Message);
                    throw;
                }
            }
        }

        /// <summary>
        /// 监控同步操作
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="operationName">操作名称</param>
        /// <param name="operation">要监控的操作</param>
        /// <returns>操作结果</returns>
        public static T Monitor<T>(string operationName, Func<T> operation)
        {
            using (var monitor = StartMonitoring(operationName))
            {
                try
                {
                    return operation();
                }
                catch (Exception ex)
                {
                    monitor.SetError(ex.Message);
                    throw;
                }
            }
        }
        #endregion

        #region 指标记录和统计
        /// <summary>
        /// 记录性能指标
        /// </summary>
        /// <param name="metric">性能指标</param>
        public static void RecordMetric(PerformanceMetric metric)
        {
            if (metric == null) return;

            // 添加到最近指标队列
            _recentMetrics.Enqueue(metric);
            
            // 限制队列大小
            while (_recentMetrics.Count > _maxRecentMetrics)
            {
                _recentMetrics.TryDequeue(out _);
            }

            // 更新统计信息
            _performanceStats.AddOrUpdate(metric.OperationName, 
                // 添加新统计
                new PerformanceStats
                {
                    OperationName = metric.OperationName,
                    TotalCount = 1,
                    SuccessCount = metric.IsSuccess ? 1 : 0,
                    FailureCount = metric.IsSuccess ? 0 : 1,
                    TotalDuration = metric.Duration,
                    MinDuration = metric.Duration,
                    MaxDuration = metric.Duration,
                    TotalMemoryUsed = metric.MemoryUsed,
                    FirstRecordTime = metric.StartTime,
                    LastRecordTime = metric.EndTime
                },
                // 更新现有统计
                (key, existing) =>
                {
                    existing.TotalCount++;
                    if (metric.IsSuccess)
                        existing.SuccessCount++;
                    else
                        existing.FailureCount++;
                    
                    existing.TotalDuration = existing.TotalDuration.Add(metric.Duration);
                    existing.TotalMemoryUsed += metric.MemoryUsed;
                    
                    if (metric.Duration < existing.MinDuration)
                        existing.MinDuration = metric.Duration;
                    if (metric.Duration > existing.MaxDuration)
                        existing.MaxDuration = metric.Duration;
                    
                    existing.LastRecordTime = metric.EndTime;
                    
                    return existing;
                });
        }

        /// <summary>
        /// 获取操作的性能统计
        /// </summary>
        /// <param name="operationName">操作名称</param>
        /// <returns>性能统计</returns>
        public static PerformanceStats GetStats(string operationName)
        {
            return _performanceStats.TryGetValue(operationName, out var stats) ? stats : null;
        }

        /// <summary>
        /// 获取所有性能统计
        /// </summary>
        /// <returns>所有性能统计</returns>
        public static Dictionary<string, PerformanceStats> GetAllStats()
        {
            return _performanceStats.ToDictionary(kv => kv.Key, kv => kv.Value);
        }

        /// <summary>
        /// 获取最近的性能指标
        /// </summary>
        /// <param name="count">获取数量</param>
        /// <returns>最近的性能指标</returns>
        public static List<PerformanceMetric> GetRecentMetrics(int count = 100)
        {
            var metrics = _recentMetrics.ToList();
            return metrics.Skip(Math.Max(0, metrics.Count - count)).ToList();
        }

        /// <summary>
        /// 获取指定操作的最近指标
        /// </summary>
        /// <param name="operationName">操作名称</param>
        /// <param name="count">获取数量</param>
        /// <returns>最近的性能指标</returns>
        public static List<PerformanceMetric> GetRecentMetrics(string operationName, int count = 100)
        {
            var filteredMetrics = _recentMetrics
                .Where(m => m.OperationName == operationName)
                .ToList();
            return filteredMetrics.Skip(Math.Max(0, filteredMetrics.Count - count)).ToList();
        }
        #endregion

        #region 性能分析
        /// <summary>
        /// 分析性能瓶颈
        /// </summary>
        /// <returns>性能分析报告</returns>
        public static string AnalyzePerformance()
        {
            var report = new System.Text.StringBuilder();
            report.AppendLine("=== 性能分析报告 ===");
            report.AppendLine($"生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine();

            var allStats = GetAllStats();
            if (!allStats.Any())
            {
                report.AppendLine("暂无性能数据");
                return report.ToString();
            }

            // 按平均耗时排序
            var sortedByDuration = allStats.Values
                .OrderByDescending(s => s.AverageDuration.TotalMilliseconds)
                .Take(10);

            report.AppendLine("🐌 耗时最长的操作 (Top 10):");
            foreach (var stat in sortedByDuration)
            {
                report.AppendLine($"  {stat.OperationName}: 平均 {stat.AverageDuration.TotalMilliseconds:F2}ms, " +
                                $"最大 {stat.MaxDuration.TotalMilliseconds:F2}ms, " +
                                $"成功率 {stat.SuccessRate:F1}%, " +
                                $"调用次数 {stat.TotalCount}");
            }

            report.AppendLine();

            // 按失败率排序
            var sortedByFailureRate = allStats.Values
                .Where(s => s.FailureCount > 0)
                .OrderByDescending(s => 100 - s.SuccessRate)
                .Take(10);

            if (sortedByFailureRate.Any())
            {
                report.AppendLine("❌ 失败率最高的操作 (Top 10):");
                foreach (var stat in sortedByFailureRate)
                {
                    report.AppendLine($"  {stat.OperationName}: 失败率 {100 - stat.SuccessRate:F1}%, " +
                                    $"失败次数 {stat.FailureCount}/{stat.TotalCount}");
                }
                report.AppendLine();
            }

            // 按内存使用排序
            var sortedByMemory = allStats.Values
                .Where(s => s.AverageMemoryUsed > 0)
                .OrderByDescending(s => s.AverageMemoryUsed)
                .Take(10);

            if (sortedByMemory.Any())
            {
                report.AppendLine("🧠 内存使用最多的操作 (Top 10):");
                foreach (var stat in sortedByMemory)
                {
                    report.AppendLine($"  {stat.OperationName}: 平均 {stat.AverageMemoryUsed / 1024.0:F2}KB, " +
                                    $"总计 {stat.TotalMemoryUsed / 1024.0:F2}KB");
                }
                report.AppendLine();
            }

            // 总体统计
            var totalOperations = allStats.Values.Sum(s => s.TotalCount);
            var totalSuccessful = allStats.Values.Sum(s => s.SuccessCount);
            var totalDuration = allStats.Values.Aggregate(TimeSpan.Zero, (sum, s) => sum.Add(s.TotalDuration));
            var totalMemory = allStats.Values.Sum(s => s.TotalMemoryUsed);

            report.AppendLine("📊 总体统计:");
            report.AppendLine($"  总操作数: {totalOperations}");
            report.AppendLine($"  成功率: {(totalOperations > 0 ? (double)totalSuccessful / totalOperations * 100 : 0):F1}%");
            report.AppendLine($"  总耗时: {totalDuration.TotalSeconds:F2}秒");
            report.AppendLine($"  平均耗时: {(totalOperations > 0 ? totalDuration.TotalMilliseconds / totalOperations : 0):F2}ms");
            report.AppendLine($"  总内存使用: {totalMemory / 1024.0 / 1024.0:F2}MB");

            return report.ToString();
        }

        /// <summary>
        /// 检测性能异常
        /// </summary>
        /// <returns>异常检测结果</returns>
        public static List<string> DetectAnomalies()
        {
            var anomalies = new List<string>();
            var allStats = GetAllStats();

            foreach (var stat in allStats.Values)
            {
                // 检测高失败率
                if (stat.TotalCount >= 10 && stat.SuccessRate < 80)
                {
                    anomalies.Add($"⚠️ {stat.OperationName}: 失败率过高 ({100 - stat.SuccessRate:F1}%)");
                }

                // 检测异常耗时
                if (stat.MaxDuration.TotalSeconds > 30)
                {
                    anomalies.Add($"🐌 {stat.OperationName}: 最大耗时异常 ({stat.MaxDuration.TotalSeconds:F1}秒)");
                }

                // 检测内存使用异常
                if (stat.AverageMemoryUsed > 50 * 1024 * 1024) // 50MB
                {
                    anomalies.Add($"🧠 {stat.OperationName}: 内存使用过高 ({stat.AverageMemoryUsed / 1024.0 / 1024.0:F1}MB)");
                }
            }

            return anomalies;
        }
        #endregion

        #region 清理和维护
        /// <summary>
        /// 清理过期数据
        /// </summary>
        /// <param name="state">定时器状态</param>
        private static void CleanupExpiredData(object state)
        {
            lock (_cleanupLock)
            {
                try
                {
                    var cutoffTime = DateTime.Now.AddHours(-24); // 保留24小时内的数据
                    
                    // 清理过期的统计数据
                    var expiredKeys = _performanceStats
                        .Where(kv => kv.Value.LastRecordTime < cutoffTime)
                        .Select(kv => kv.Key)
                        .ToList();

                    foreach (var key in expiredKeys)
                    {
                        _performanceStats.TryRemove(key, out _);
                    }

                    // 清理过期的最近指标
                    var expiredMetrics = new List<PerformanceMetric>();
                    while (_recentMetrics.TryPeek(out var metric) && metric.EndTime < cutoffTime)
                    {
                        if (_recentMetrics.TryDequeue(out var expiredMetric))
                        {
                            expiredMetrics.Add(expiredMetric);
                        }
                    }

                    if (expiredKeys.Count > 0 || expiredMetrics.Count > 0)
                    {
                        ETLogManager.Info($"性能数据清理完成: 清理了{expiredKeys.Count}个统计项和{expiredMetrics.Count}个指标");
                    }
                }
                catch (Exception ex)
                {
                    ETLogManager.Error($"性能数据清理失败: {ex.Message}", ex);
                }
            }
        }

        /// <summary>
        /// 清除所有性能数据
        /// </summary>
        public static void ClearAllData()
        {
            _performanceStats.Clear();
            while (_recentMetrics.TryDequeue(out _)) { }
            
            ETLogManager.Info("所有性能数据已清除");
        }

        /// <summary>
        /// 获取性能数据摘要
        /// </summary>
        /// <returns>性能数据摘要</returns>
        public static (int TotalOperations, int UniqueOperations, TimeSpan TotalDuration, long TotalMemory) GetSummary()
        {
            var allStats = GetAllStats();
            var totalOperations = allStats.Values.Sum(s => s.TotalCount);
            var uniqueOperations = allStats.Count;
            var totalDuration = allStats.Values.Aggregate(TimeSpan.Zero, (sum, s) => sum.Add(s.TotalDuration));
            var totalMemory = allStats.Values.Sum(s => s.TotalMemoryUsed);

            return (totalOperations, uniqueOperations, totalDuration, totalMemory);
        }
        #endregion
    }
}
