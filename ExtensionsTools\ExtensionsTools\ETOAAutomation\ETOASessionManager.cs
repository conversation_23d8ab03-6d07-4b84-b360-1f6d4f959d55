using System;
using System.Threading;
using System.Threading.Tasks;
using ET.ETOAAutomation.Models;
using ET.ETOAAutomation.Storage;
using ET.ETOAAutomation.Helpers;
using ET;

namespace ET.ETOAAutomation
{
    /// <summary>
    /// 会话状态管理器，负责维护登录状态和定期刷新
    /// </summary>
    public class ETOASessionManager : IDisposable
    {
        #region 私有字段

        private readonly ETOAApiClient _apiClient;
        private readonly ETOASessionStorage _sessionStorage;
        private ETOAAutoReloginHelper _autoReloginHelper;
        private Timer _heartbeatTimer;
        private bool _isMonitoring = false;
        private long _lastHeartbeat = DateTime.MinValue.ToBinary(); // 使用long存储以支持原子操作
        private int _heartbeatInterval = 300; // 默认5分钟
        private ETOASessionData _currentSession;
        private string _currentUsername;
        private string _currentPassword;
        private string _baseUrl;
        private bool _autoReloginEnabled = true;
        private int _maxReloginAttempts = 3;
        private long _currentReloginAttempts = 0; // 使用long支持原子操作
        private readonly object _sessionLock = new object();
        private int _isHeartbeatRunning = 0; // 心跳执行状态标志 (0=false, 1=true)
        private volatile bool _disposed = false; // 资源释放状态标志
        private volatile bool _isAutoReloginRunning = false; // 自动重登执行状态标志
        private readonly object _reloginLock = new object(); // 重登操作锁

        #endregion 私有字段

        #region 公共属性

        /// <summary>
        /// 是否正在监控（线程安全访问）
        /// </summary>
        public bool IsMonitoring
        {
            get
            {
                lock (_sessionLock)
                {
                    return _isMonitoring;
                }
            }
        }

        /// <summary>
        /// 最后心跳时间
        /// </summary>
        public DateTime LastHeartbeat => DateTime.FromBinary(Interlocked.Read(ref _lastHeartbeat));

        /// <summary>
        /// 心跳间隔（秒）
        /// </summary>
        public int HeartbeatInterval
        {
            get => _heartbeatInterval;
            set => _heartbeatInterval = Math.Max(60, value); // 最小1分钟
        }

        /// <summary>
        /// 当前会话数据（线程安全访问）
        /// </summary>
        public ETOASessionData CurrentSession
        {
            get
            {
                lock (_sessionLock)
                {
                    return _currentSession?.Clone(); // 返回副本避免外部修改
                }
            }
        }

        /// <summary>
        /// 是否启用自动重登
        /// </summary>
        public bool AutoReloginEnabled
        {
            get => _autoReloginEnabled;
            set => _autoReloginEnabled = value;
        }

        /// <summary>
        /// 最大重登尝试次数
        /// </summary>
        public int MaxReloginAttempts
        {
            get => _maxReloginAttempts;
            set => _maxReloginAttempts = Math.Max(1, value);
        }

        /// <summary>
        /// 当前重登尝试次数（线程安全访问）
        /// </summary>
        public int CurrentReloginAttempts => (int)Interlocked.Read(ref _currentReloginAttempts);

        #endregion 公共属性

        #region 事件定义

        /// <summary>
        /// 会话状态变更事件
        /// </summary>
        public event EventHandler<SessionStatusChangedEventArgs> SessionStatusChanged;

        /// <summary>
        /// 心跳失败事件
        /// </summary>
        public event EventHandler<HeartbeatFailedEventArgs> HeartbeatFailed;

        /// <summary>
        /// 自动重登事件
        /// </summary>
        public event EventHandler<AutoReloginEventArgs> AutoReloginAttempted;

        #endregion 事件定义

        #region 构造函数

        /// <summary>
        /// 初始化会话管理器
        /// </summary>
        /// <param name="apiClient">API客户端</param>
        /// <param name="baseUrl">OA系统基础URL</param>
        public ETOASessionManager(ETOAApiClient apiClient, string baseUrl = "")
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _sessionStorage = new ETOASessionStorage();
            _baseUrl = baseUrl;

            // 初始化自动重登辅助类
            if (!string.IsNullOrEmpty(_baseUrl))
            {
                _autoReloginHelper = new ETOAAutoReloginHelper(_apiClient, this, _baseUrl);

                // 订阅自动重登事件
                _autoReloginHelper.ReloginCompleted += OnAutoReloginCompleted;
                _autoReloginHelper.ReloginFailed += OnAutoReloginFailed;
            }

            ETLogManager.Info("ETOASessionManager初始化完成");
        }

        #endregion 构造函数

        #region 私有方法

        /// <summary>
        /// 心跳回调方法
        /// </summary>
        /// <param name="state">状态对象</param>
        private async void HeartbeatCallback(object state)
        {
            // 检查是否已释放或正在执行心跳
            if (_disposed || _isHeartbeatRunning != 0)
            {
                return;
            }

            // 设置心跳执行状态
            if (Interlocked.CompareExchange(ref _isHeartbeatRunning, 1, 0) != 0)
            {
                return; // 已有心跳在执行，跳过本次
            }

            try
            {
                ETLogManager.Debug("开始执行心跳检查");

                var isValid = await IsSessionValidAsync();

                // 线程安全地更新最后心跳时间
                Interlocked.Exchange(ref _lastHeartbeat, DateTime.Now.ToBinary());

                lock (_sessionLock)
                {
                    if (_currentSession != null && !_disposed)
                    {
                        _currentSession.RecordHeartbeat(isValid);

                        // 保存会话状态
                        _sessionStorage.SaveSessionData(_currentSession);
                    }
                }

                if (!isValid && !_disposed)
                {
                    ETLogManager.Warning("会话无效，触发心跳失败事件");
                    OnHeartbeatFailed(new HeartbeatFailedEventArgs
                    {
                        Timestamp = DateTime.Now,
                        Reason = "会话验证失败"
                    });

                    // 尝试刷新会话
                    var refreshSuccess = await RefreshSessionAsync();
                    if (!refreshSuccess && _autoReloginEnabled && !_disposed)
                    {
                        // 刷新失败，尝试自动重登（异步执行，避免阻塞定时器）
                        _ = Task.Run(async () => await AttemptAutoReloginAsync());
                    }
                }
                else if (isValid)
                {
                    // 线程安全地重置重登尝试次数
                    Interlocked.Exchange(ref _currentReloginAttempts, 0);
                    ETLogManager.Debug("心跳检查成功");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("心跳检查失败", ex);
                if (!_disposed)
                {
                    OnHeartbeatFailed(new HeartbeatFailedEventArgs
                    {
                        Timestamp = DateTime.Now,
                        Reason = ex.Message,
                        Exception = ex
                    });
                }
            }
            finally
            {
                // 重置心跳执行状态
                Interlocked.Exchange(ref _isHeartbeatRunning, 0);
            }
        }

        /// <summary>
        /// 尝试自动重登
        /// </summary>
        private async Task AttemptAutoReloginAsync()
        {
            // 检查是否已释放或正在执行重登
            if (_disposed || _isAutoReloginRunning)
            {
                ETLogManager.Debug("跳过自动重登：系统已释放或重登正在进行中");
                return;
            }

            // 使用锁防止并发重登
            lock (_reloginLock)
            {
                if (_isAutoReloginRunning)
                {
                    ETLogManager.Debug("跳过自动重登：重登已在进行中");
                    return;
                }
                _isAutoReloginRunning = true;
            }

            try
            {
                // 线程安全地检查重登次数
                var currentAttempts = Interlocked.Read(ref _currentReloginAttempts);
                if (currentAttempts >= _maxReloginAttempts)
                {
                    ETLogManager.Error($"自动重登尝试次数已达上限 ({_maxReloginAttempts})，停止尝试");
                    OnAutoReloginAttempted(new AutoReloginEventArgs
                    {
                        Success = false,
                        AttemptNumber = (int)currentAttempts,
                        Reason = "达到最大重试次数"
                    });
                    return;
                }

                // 原子递增重登尝试次数
                var attemptNumber = Interlocked.Increment(ref _currentReloginAttempts);
                ETLogManager.Info($"开始第 {attemptNumber} 次自动重登尝试");

                if (_autoReloginHelper != null && _autoReloginHelper.CanExecuteAutoRelogin())
                {
                    // 使用自动重登辅助类执行重登
                    var success = await _autoReloginHelper.ExecuteAutoReloginAsync((int)attemptNumber);

                    OnAutoReloginAttempted(new AutoReloginEventArgs
                    {
                        Success = success,
                        AttemptNumber = (int)attemptNumber,
                        Reason = success ? "自动重登成功" : "自动重登失败"
                    });

                    if (success)
                    {
                        // 重置重登尝试次数
                        Interlocked.Exchange(ref _currentReloginAttempts, 0);
                    }
                }
                else
                {
                    // 通过事件通知外部进行重登
                    OnAutoReloginAttempted(new AutoReloginEventArgs
                    {
                        Success = false,
                        AttemptNumber = (int)attemptNumber,
                        Reason = "自动重登辅助类不可用或条件不满足"
                    });
                }
            }
            catch (Exception ex)
            {
                var currentAttempts = Interlocked.Read(ref _currentReloginAttempts);
                ETLogManager.Error($"自动重登失败 (第 {currentAttempts} 次)", ex);
                OnAutoReloginAttempted(new AutoReloginEventArgs
                {
                    Success = false,
                    AttemptNumber = (int)currentAttempts,
                    Reason = ex.Message,
                    Exception = ex
                });
            }
            finally
            {
                // 重置重登执行状态
                lock (_reloginLock)
                {
                    _isAutoReloginRunning = false;
                }
            }
        }

        /// <summary>
        /// 自动重登完成事件处理
        /// </summary>
        private void OnAutoReloginCompleted(object sender, ReloginCompletedEventArgs e)
        {
            if (e.Success)
            {
                ETLogManager.Info($"自动重登成功，用户: {e.Username}");
                Interlocked.Exchange(ref _currentReloginAttempts, 0);

                // 重新启动会话监控
                bool shouldStartMonitoring = false;
                lock (_sessionLock)
                {
                    shouldStartMonitoring = !_isMonitoring;
                }

                if (shouldStartMonitoring)
                {
                    _ = StartSessionMonitoringAsync();
                }
            }
        }

        /// <summary>
        /// 自动重登失败事件处理
        /// </summary>
        private void OnAutoReloginFailed(object sender, ReloginFailedEventArgs e)
        {
            ETLogManager.Error($"自动重登失败，第 {e.AttemptNumber} 次尝试：{e.Reason}");
        }

        /// <summary>
        /// 触发会话状态变更事件
        /// </summary>
        private void OnSessionStatusChanged(SessionStatusChangedEventArgs e)
        {
            SessionStatusChanged?.Invoke(this, e);
        }

        /// <summary>
        /// 触发心跳失败事件
        /// </summary>
        private void OnHeartbeatFailed(HeartbeatFailedEventArgs e)
        {
            HeartbeatFailed?.Invoke(this, e);
        }

        /// <summary>
        /// 触发自动重登事件
        /// </summary>
        private void OnAutoReloginAttempted(AutoReloginEventArgs e)
        {
            AutoReloginAttempted?.Invoke(this, e);
        }

        #endregion 私有方法

        #region 公共方法

        /// <summary>
        /// 初始化会话（设置用户凭据）
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        /// <param name="sessionData">会话数据</param>
        public void InitializeSession(string username, string password, ETOASessionData sessionData = null)
        {
            try
            {
                _currentUsername = username;
                _currentPassword = password;

                // 设置自动重登凭据
                if (_autoReloginHelper != null)
                {
                    _autoReloginHelper.SetCredentials(username, password);
                }

                lock (_sessionLock)
                {
                    if (sessionData != null)
                    {
                        _currentSession = sessionData;
                    }
                    else
                    {
                        _currentSession = new ETOASessionData
                        {
                            Username = username,
                            CreatedTime = DateTime.Now,
                            LastActivityTime = DateTime.Now,
                            HeartbeatInterval = _heartbeatInterval
                        };
                    }
                }

                ETLogManager.Info($"会话初始化完成，用户: {username}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("会话初始化失败", ex);
                throw new ETException("会话初始化失败", "ETOASessionManager.InitializeSession", ex);
            }
        }

        /// <summary>
        /// 开始会话监控
        /// </summary>
        /// <returns>启动是否成功</returns>
        public async Task<bool> StartSessionMonitoringAsync()
        {
            try
            {
                lock (_sessionLock)
                {
                    if (_isMonitoring)
                    {
                        ETLogManager.Warning("会话监控已在运行中");
                        return true;
                    }
                }

                if (_currentSession == null)
                {
                    ETLogManager.Error("无法启动会话监控：会话未初始化");
                    return false;
                }

                // 尝试恢复之前的会话状态
                LoadSessionState();

                // 启动定时器
                _heartbeatTimer = new Timer(
                    HeartbeatCallback,
                    null,
                    TimeSpan.FromSeconds(10), // 10秒后开始第一次心跳
                    TimeSpan.FromSeconds(_heartbeatInterval)
                );

                lock (_sessionLock)
                {
                    _isMonitoring = true;
                    Interlocked.Exchange(ref _lastHeartbeat, DateTime.Now.ToBinary());

                    if (_currentSession != null)
                    {
                        _currentSession.Status = SessionStatus.Active;
                        OnSessionStatusChanged(new SessionStatusChangedEventArgs
                        {
                            OldStatus = SessionStatus.Idle,
                            NewStatus = SessionStatus.Active,
                            SessionId = _currentSession.SessionId
                        });
                    }
                }

                ETLogManager.Info($"会话监控启动成功，心跳间隔: {_heartbeatInterval}秒");
                return true;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("启动会话监控失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 停止会话监控
        /// </summary>
        public void StopSessionMonitoring()
        {
            try
            {
                _heartbeatTimer?.Dispose();
                _heartbeatTimer = null;

                lock (_sessionLock)
                {
                    _isMonitoring = false;

                    if (_currentSession != null)
                    {
                        _currentSession.Status = SessionStatus.Idle;
                        OnSessionStatusChanged(new SessionStatusChangedEventArgs
                        {
                            OldStatus = SessionStatus.Active,
                            NewStatus = SessionStatus.Idle,
                            SessionId = _currentSession.SessionId
                        });

                        // 保存会话状态
                        _sessionStorage.SaveSessionData(_currentSession);
                    }
                }

                ETLogManager.Info("会话监控已停止");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("停止会话监控时发生错误", ex);
            }
        }

        /// <summary>
        /// 刷新会话
        /// </summary>
        /// <returns>刷新是否成功</returns>
        public async Task<bool> RefreshSessionAsync()
        {
            try
            {
                ETLogManager.Info("开始刷新会话");

                // 发送刷新请求到服务器 这里需要根据具体OA系统的刷新机制实现
                var response = await _apiClient.GetAsync<object>("/api/session/refresh");

                if (response != null)
                {
                    lock (_sessionLock)
                    {
                        if (_currentSession != null)
                        {
                            _currentSession.RefreshSession();
                            _sessionStorage.SaveSessionData(_currentSession);
                        }
                    }

                    ETLogManager.Info("会话刷新成功");
                    return true;
                }

                ETLogManager.Warning("会话刷新失败：服务器响应为空");
                return false;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("刷新会话失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 检查会话是否有效
        /// </summary>
        /// <returns>会话是否有效</returns>
        public async Task<bool> IsSessionValidAsync()
        {
            try
            {
                // 首先检查本地会话状态
                lock (_sessionLock)
                {
                    if (_currentSession == null || _currentSession.IsExpired || !_currentSession.IsValid)
                    {
                        ETLogManager.Debug("本地会话状态无效");
                        return false;
                    }
                }

                if (!_apiClient.IsAuthenticated)
                {
                    ETLogManager.Debug("API客户端未认证");
                    return false;
                }

                // 发送验证请求到服务器
                var response = await _apiClient.GetAsync<object>("/api/session/validate");

                bool isValid = response != null;
                ETLogManager.Debug($"服务器会话验证结果: {isValid}");

                return isValid;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("会话验证失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 加载会话状态
        /// </summary>
        private void LoadSessionState()
        {
            try
            {
                if (string.IsNullOrEmpty(_currentUsername))
                {
                    return;
                }

                // 尝试从存储中加载会话数据
                var sessionIds = _sessionStorage.GetAllSessionIds();
                foreach (var sessionId in sessionIds)
                {
                    var sessionData = _sessionStorage.LoadSessionData(sessionId);
                    if (sessionData != null && sessionData.Username == _currentUsername && !sessionData.IsExpired)
                    {
                        lock (_sessionLock)
                        {
                            _currentSession = sessionData;
                        }

                        ETLogManager.Info($"成功恢复会话状态，SessionId: {sessionId}");
                        return;
                    }
                }

                ETLogManager.Info("未找到有效的历史会话，将创建新会话");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("加载会话状态失败", ex);
            }
        }

        /// <summary>
        /// 保存会话状态
        /// </summary>
        public void SaveSessionState()
        {
            try
            {
                lock (_sessionLock)
                {
                    if (_currentSession != null)
                    {
                        _sessionStorage.SaveSessionData(_currentSession);
                        ETLogManager.Debug($"会话状态已保存，SessionId: {_currentSession.SessionId}");
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("保存会话状态失败", ex);
            }
        }

        /// <summary>
        /// 清理过期会话
        /// </summary>
        public void CleanupExpiredSessions()
        {
            try
            {
                _sessionStorage.CleanExpiredSessions();
                ETLogManager.Info("过期会话清理完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("清理过期会话失败", ex);
            }
        }

        /// <summary>
        /// 获取会话统计信息
        /// </summary>
        public SessionStatistics GetSessionStatistics()
        {
            try
            {
                var stats = new SessionStatistics();

                lock (_sessionLock)
                {
                    if (_currentSession != null)
                    {
                        stats.CurrentSessionId = _currentSession.SessionId;
                        stats.SessionAge = DateTime.Now - _currentSession.CreatedTime;
                        stats.LastActivity = _currentSession.LastActivityTime;
                        stats.IsActive = _currentSession.Status == SessionStatus.Active;
                        stats.HeartbeatFailures = _currentSession.HeartbeatFailureCount;
                        stats.RemainingTime = TimeSpan.FromMinutes(_currentSession.RemainingMinutes);
                    }
                }

                lock (_sessionLock)
                {
                    stats.IsMonitoring = _isMonitoring;
                }
                stats.LastHeartbeat = DateTime.FromBinary(Interlocked.Read(ref _lastHeartbeat));
                stats.HeartbeatInterval = TimeSpan.FromSeconds(_heartbeatInterval);
                stats.ReloginAttempts = (int)Interlocked.Read(ref _currentReloginAttempts);
                stats.MaxReloginAttempts = _maxReloginAttempts;

                return stats;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("获取会话统计信息失败", ex);
                return new SessionStatistics();
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
            {
                return; // 避免重复释放
            }

            try
            {
                // 设置释放标志
                _disposed = true;

                // 停止会话监控
                StopSessionMonitoring();

                // 等待心跳和重登操作完成
                var timeout = TimeSpan.FromSeconds(5);
                var startTime = DateTime.Now;
                while ((_isHeartbeatRunning != 0 || _isAutoReloginRunning) && DateTime.Now - startTime < timeout)
                {
                    Thread.Sleep(100);
                }

                // 保存会话状态
                SaveSessionState();

                // 释放定时器
                _heartbeatTimer?.Dispose();
                _heartbeatTimer = null;

                // 清理自动重登辅助类
                if (_autoReloginHelper != null)
                {
                    _autoReloginHelper.ReloginCompleted -= OnAutoReloginCompleted;
                    _autoReloginHelper.ReloginFailed -= OnAutoReloginFailed;
                    _autoReloginHelper.ClearCredentials();
                    _autoReloginHelper = null;
                }

                ETLogManager.Info("ETOASessionManager资源释放完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("释放ETOASessionManager资源时发生错误", ex);
            }
        }

        #endregion 公共方法
    }

    #region 事件参数类

    /// <summary>
    /// 会话状态变更事件参数
    /// </summary>
    public class SessionStatusChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 旧状态
        /// </summary>
        public SessionStatus OldStatus { get; set; }

        /// <summary>
        /// 新状态
        /// </summary>
        public SessionStatus NewStatus { get; set; }

        /// <summary>
        /// 会话ID
        /// </summary>
        public string SessionId { get; set; }

        /// <summary>
        /// 变更时间
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 心跳失败事件参数
    /// </summary>
    public class HeartbeatFailedEventArgs : EventArgs
    {
        /// <summary>
        /// 失败时间
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 失败原因
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// 异常信息
        /// </summary>
        public Exception Exception { get; set; }
    }

    /// <summary>
    /// 自动重登事件参数
    /// </summary>
    public class AutoReloginEventArgs : EventArgs
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 尝试次数
        /// </summary>
        public int AttemptNumber { get; set; }

        /// <summary>
        /// 原因或错误信息
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// 异常信息
        /// </summary>
        public Exception Exception { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 会话统计信息
    /// </summary>
    public class SessionStatistics
    {
        /// <summary>
        /// 当前会话ID
        /// </summary>
        public string CurrentSessionId { get; set; }

        /// <summary>
        /// 会话存活时间
        /// </summary>
        public TimeSpan SessionAge { get; set; }

        /// <summary>
        /// 最后活动时间
        /// </summary>
        public DateTime LastActivity { get; set; }

        /// <summary>
        /// 是否活跃
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 心跳失败次数
        /// </summary>
        public int HeartbeatFailures { get; set; }

        /// <summary>
        /// 剩余时间
        /// </summary>
        public TimeSpan RemainingTime { get; set; }

        /// <summary>
        /// 是否正在监控
        /// </summary>
        public bool IsMonitoring { get; set; }

        /// <summary>
        /// 最后心跳时间
        /// </summary>
        public DateTime LastHeartbeat { get; set; }

        /// <summary>
        /// 心跳间隔
        /// </summary>
        public TimeSpan HeartbeatInterval { get; set; }

        /// <summary>
        /// 重登尝试次数
        /// </summary>
        public int ReloginAttempts { get; set; }

        /// <summary>
        /// 最大重登尝试次数
        /// </summary>
        public int MaxReloginAttempts { get; set; }
    }

    #endregion 事件参数类
}