﻿2025-08-03 11:15:55 [INFO] 显示设置已变更
2025-08-03 11:15:56 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 11:15:57 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 111876918
2025-08-03 11:15:57 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 111876918)
2025-08-03 11:15:57 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 11:15:57 [INFO] 显示设置变更后TopForm关系已重建
2025-08-03 12:36:08 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 12:36:08 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 111876918, 新父窗口: 7742166
2025-08-03 12:36:08 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-03 12:36:08 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 12:36:08 [INFO] App_WorkbookActivate: 工作簿 '一点一案上传方案信息表-20250801-170610.xlsx' 激活处理完成
2025-08-03 12:36:08 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 12:36:08 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 7742166)
2025-08-03 12:36:08 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 12:36:08 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-03 12:36:08 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 12:36:08 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-03 12:36:08 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-03 12:36:08 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 12:36:08 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-03 12:36:08 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 12:36:09 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-03 12:36:09 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-03 12:36:09 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 12:36:09 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 12:36:09 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 7742166, 新父窗口: 111876918
2025-08-03 12:36:09 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 111876918)
2025-08-03 12:36:09 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 12:36:09 [INFO] App_WorkbookActivate: 工作簿 '场景清单_专用场景_ad60d545-8ea4-4b24-bd64-e2b8352ed1a1.csv' 激活处理完成
2025-08-03 12:36:09 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 12:36:09 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 111876918)
2025-08-03 12:36:09 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 12:36:09 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-03 12:36:10 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 12:36:10 [WARN] 检测到Excel窗口句柄变化: 7742166 -> 111876918
2025-08-03 12:36:10 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 111876918
2025-08-03 12:36:10 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 12:36:10 [ERROR] SetTop: 设置父窗口关系失败 - 期望: 111876918, 实际: 65552
2025-08-03 12:36:10 [WARN] 父子窗口关系不正确. 当前父窗口: 65552, 期望父窗口: 111876918
2025-08-03 12:36:10 [WARN] 检测到父子窗口关系异常，尝试修复
2025-08-03 12:36:10 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 111876918
2025-08-03 12:36:10 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 111876918)
2025-08-03 12:36:10 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 111876918)
2025-08-03 12:36:10 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 111876918)
2025-08-03 12:36:10 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 12:36:10 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-03 12:36:10 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 12:50:50 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-08-03 12:50:50 [INFO] 系统事件监控已停止
2025-08-03 12:50:50 [INFO] Excel窗口句柄监控已停止
2025-08-03 12:50:50 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-08-03 12:50:51 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 12:50:51 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-03 12:50:51 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-03 12:50:51 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 7742166
2025-08-03 12:50:51 [INFO] 系统事件监控已启动
2025-08-03 12:50:51 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 12:50:51 [INFO] App_WorkbookActivate: 工作簿 '一点一案上传方案信息表-20250801-170610.xlsx' 激活处理完成
2025-08-03 12:50:51 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 12:50:51 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 7742166)
2025-08-03 12:50:51 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 12:50:51 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-03 12:50:52 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 12:50:52 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-03 12:50:52 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-03 12:50:52 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 12:50:52 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-03 12:50:52 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 12:50:52 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-03 12:50:52 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-03 12:50:52 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 12:51:29 [INFO] App_WorkbookOpen: 工作簿 '2CC扩容节点清单(2).xlsx' 打开事件触发
2025-08-03 12:51:29 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 12:51:29 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 7742166, 新父窗口: 89790590
2025-08-03 12:51:29 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 89790590)
2025-08-03 12:51:29 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 12:51:29 [INFO] App_WorkbookOpen: 工作簿 '2CC扩容节点清单(2).xlsx' 打开处理完成
2025-08-03 12:51:29 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 12:51:29 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 89790590)
2025-08-03 12:51:29 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 12:51:29 [INFO] App_WorkbookActivate: 工作簿 '2CC扩容节点清单(2).xlsx' 激活处理完成
2025-08-03 12:51:29 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 12:51:29 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 89790590)
2025-08-03 12:51:29 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 12:51:29 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-03 12:51:30 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 12:51:30 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 89790590
2025-08-03 12:51:30 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 12:51:30 [ERROR] SetTop: 设置父窗口关系失败 - 期望: 89790590, 实际: 65552
2025-08-03 12:51:30 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 12:51:30 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-03 12:51:30 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 89790590
2025-08-03 12:51:30 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 89790590)
2025-08-03 12:51:30 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 12:51:30 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 12:51:30 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 89790590
2025-08-03 12:51:30 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 89790590)
2025-08-03 12:51:30 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 12:51:30 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-08-03 13:04:43 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-08-03 13:04:43 [INFO] 系统事件监控已停止
2025-08-03 13:04:43 [INFO] Excel窗口句柄监控已停止
2025-08-03 13:04:43 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-08-03 13:04:44 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 13:04:44 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-03 13:04:44 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-03 13:04:44 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 7742166
2025-08-03 13:04:44 [INFO] 系统事件监控已启动
2025-08-03 13:04:44 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 13:04:44 [INFO] App_WorkbookActivate: 工作簿 '一点一案上传方案信息表-20250801-170610.xlsx' 激活处理完成
2025-08-03 13:04:44 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 13:04:44 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 7742166)
2025-08-03 13:04:44 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 13:04:44 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-03 13:04:45 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 13:04:45 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-03 13:04:45 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-03 13:04:45 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 13:04:45 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-03 13:04:45 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 13:04:45 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-03 13:04:45 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-03 13:04:45 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 13:32:28 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 13:32:28 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 7742166)
2025-08-03 13:32:28 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 14:30:37 [INFO] 用户取消了文件选择
2025-08-03 14:30:40 [INFO] 用户取消了文件选择
2025-08-03 14:30:41 [INFO] OpenForm: 保持窗体现有标题 'Excel文件记录管理（双击文件名打开文件）'
2025-08-03 14:30:41 [INFO] OpenForm: 准备打开窗体 'Excel文件记录管理（双击文件名打开文件）'，位置: Center，单实例: True
2025-08-03 14:30:41 [INFO] 开始显示窗体 'Excel文件记录管理（双击文件名打开文件）'，位置模式: Center
2025-08-03 14:30:41 [INFO] 窗体 'Excel文件记录管理（双击文件名打开文件）' 以TopMostForm为父窗体显示
2025-08-03 14:30:41 [INFO] 窗体 'Excel文件记录管理（双击文件名打开文件）' 显示完成，句柄: 38014364
2025-08-03 14:30:41 [INFO] OpenForm: 窗体 'Excel文件记录管理（双击文件名打开文件）' 打开成功
2025-08-03 14:33:58 [INFO] Excel窗口句柄监控器初始化完成
2025-08-03 14:33:59 [INFO] 配置文件实例已在加载时初始化
2025-08-03 14:33:59 [INFO] 开始保存原始控件标题（避免后续被混淆）
2025-08-03 14:33:59 [WARN] UI权限管理器未初始化，无法保存原始控件标题
2025-08-03 14:33:59 [INFO] Ribbon加载完成，原始标题已保存，等待权限管理器初始化后再更正控件标题
2025-08-03 14:33:59 [INFO] 成功初始化Excel应用程序实例
2025-08-03 14:33:59 [INFO] 自动备份路径未配置
2025-08-03 14:33:59 [DEBUG] 开始初始化授权控制器
2025-08-03 14:33:59 [DEBUG] 授权系统初始化完成，耗时: 479ms
2025-08-03 14:33:59 [DEBUG] 开始初始化授权验证
2025-08-03 14:33:59 [INFO] 全局映射管理器已设置: HyControlMappingManager
2025-08-03 14:33:59 [DEBUG] 权限管理器初始化成功
2025-08-03 14:33:59 [DEBUG] 使用新的权限管理器进行初始化
2025-08-03 14:33:59 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-08-03 14:33:59 [INFO] 开始初始化UI权限管理
2025-08-03 14:33:59 [DEBUG] [实例ID: 6b0d571a] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-08-03 14:33:59 [DEBUG] 🔍 [实例ID: 6b0d571a] 字典引用一致性检查:
2025-08-03 14:33:59 [DEBUG] 🔍   标题映射一致性: True
2025-08-03 14:33:59 [DEBUG] 🔍   权限映射一致性: True
2025-08-03 14:33:59 [DEBUG] 🔍   信息映射一致性: True
2025-08-03 14:33:59 [DEBUG] 🔍   特殊控件一致性: True
2025-08-03 14:33:59 [DEBUG] 控件权限管理器初始化完成 [实例ID: 6b0d571a]
2025-08-03 14:33:59 [DEBUG] 开始注册控件权限映射
2025-08-03 14:33:59 [INFO] 开始初始化全局控件映射
2025-08-03 14:33:59 [DEBUG] 开始动态生成控件标题映射（从原始控件获取，避免硬编码）
2025-08-03 14:33:59 [DEBUG] 开始生成控件标题映射
2025-08-03 14:33:59 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-08-03 14:33:59 [DEBUG] 通过反射获取到 124 个字段
2025-08-03 14:33:59 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-08-03 14:34:00 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-08-03 14:34:00 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-08-03 14:34:00 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-08-03 14:34:00 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-08-03 14:34:00 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [INFO] 控件结构获取完成，共获取到 122 个控件
2025-08-03 14:34:00 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-08-03 14:34:00 [INFO] 🔍 处理znAbout控件标题映射: znAboutGroup, Label: '授权', IsEmpty: False
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件 znAboutGroup 标题映射已添加: '授权'
2025-08-03 14:34:00 [INFO] 🔍 处理znAbout控件标题映射: znAboutButton, Label: '授权', IsEmpty: False
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件 znAboutButton 标题映射已添加: '授权'
2025-08-03 14:34:00 [INFO] 🔍 处理znAbout控件标题映射: znAbout, Label: 'ZnAbout', IsEmpty: False
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件 znAbout 标题映射已添加: 'ZnAbout'
2025-08-03 14:34:00 [INFO] 控件标题映射生成完成，共生成 109 项映射
2025-08-03 14:34:00 [INFO] 🔍 最终标题映射中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-08-03 14:34:00 [DEBUG] 全局控件标题映射生成完成，共生成 109 项
2025-08-03 14:34:00 [INFO] 关键控件标题映射: hyTab -> Develop
2025-08-03 14:34:00 [INFO] 关键控件标题映射: znTab -> ZnTools
2025-08-03 14:34:00 [WARN] 关键控件未找到标题映射: buttonAbout
2025-08-03 14:34:00 [INFO] 关键控件标题映射: znAbout -> ZnAbout
2025-08-03 14:34:00 [INFO] 关键控件标题映射: znAboutGroup -> 授权
2025-08-03 14:34:00 [INFO] 关键控件标题映射: znAboutButton -> 授权
2025-08-03 14:34:00 [INFO] === znAbout控件标题映射诊断 ===
2025-08-03 14:34:00 [INFO] ✓ znAbout 标题映射存在: 'ZnAbout'
2025-08-03 14:34:00 [INFO] ✓ znAboutGroup 标题映射存在: '授权'
2025-08-03 14:34:00 [INFO] ✓ znAboutButton 标题映射存在: '授权'
2025-08-03 14:34:00 [DEBUG] === 所有生成的控件标题映射 ===
2025-08-03 14:34:00 [DEBUG] 控件映射: btm工作表管理 -> '工作表管理'
2025-08-03 14:34:00 [DEBUG] 控件映射: btnAngleExtractor -> '方向角/下倾角提取'
2025-08-03 14:34:00 [DEBUG] 控件映射: btnStationConverter -> '站点系统数量统计'
2025-08-03 14:34:00 [DEBUG] 控件映射: btnStationDataProcessor -> '基站台账数据转换处理'
2025-08-03 14:34:00 [DEBUG] 控件映射: btnTowerAccountProcessor -> '铁塔内部台账转换工具'
2025-08-03 14:34:00 [DEBUG] 控件映射: btn标记提取规整字符串a -> '标记/提取/规整字符'
2025-08-03 14:34:00 [DEBUG] 控件映射: btn标记提取规整字符串b -> '标记/提取/规整字符'
2025-08-03 14:34:00 [DEBUG] 控件映射: btn发送及存档 -> '临时/发送/存档'
2025-08-03 14:34:00 [DEBUG] 控件映射: btn格式化经纬度 -> '经纬度工具'
2025-08-03 14:34:00 [DEBUG] 控件映射: btn金额转大写 -> '金额转大写'
2025-08-03 14:34:00 [DEBUG] 控件映射: btn批量查找 -> '批量查找'
2025-08-03 14:34:00 [DEBUG] 控件映射: btn设置倍数行高 -> '设置倍数行高'
2025-08-03 14:34:00 [DEBUG] 控件映射: btn设置页眉脚 -> '设置页眉脚'
2025-08-03 14:34:00 [DEBUG] 控件映射: btn填写合规检查 -> '填写合规性检查'
2025-08-03 14:34:00 [DEBUG] 控件映射: btn填写合规性检查abc -> '填写合规性检查'
2025-08-03 14:34:00 [DEBUG] 控件映射: btn隐藏范围外内容 -> '隐藏选区外'
2025-08-03 14:34:00 [DEBUG] 控件映射: btn自动脚本 -> '自动脚本'
2025-08-03 14:34:00 [DEBUG] 控件映射: button1 -> 'ini配置文件'
2025-08-03 14:34:00 [DEBUG] 控件映射: button10 -> '记录当前文件'
2025-08-03 14:34:00 [DEBUG] 控件映射: button11 -> '删除外部链接'
2025-08-03 14:34:00 [DEBUG] 控件映射: button12 -> '设置页眉脚'
2025-08-03 14:34:00 [DEBUG] 控件映射: button13 -> '设置倍数行高'
2025-08-03 14:34:00 [DEBUG] 控件映射: button14 -> '临时/发送/存档'
2025-08-03 14:34:00 [DEBUG] 控件映射: button15 -> '订单文件生成kml图层'
2025-08-03 14:34:00 [DEBUG] 控件映射: button16 -> '批量查找站点'
2025-08-03 14:34:00 [DEBUG] 控件映射: button17 -> '向下填充'
2025-08-03 14:34:00 [DEBUG] 控件映射: button2 -> 'Excel修复'
2025-08-03 14:34:00 [DEBUG] 控件映射: button20 -> 'Excel修复'
2025-08-03 14:34:00 [DEBUG] 控件映射: button23 -> '生成地理图层'
2025-08-03 14:34:00 [DEBUG] 控件映射: button24 -> '格式化经纬度'
2025-08-03 14:34:00 [DEBUG] 控件映射: button26 -> '重置单元格备注大小'
2025-08-03 14:34:00 [DEBUG] 控件映射: button3 -> '关于'
2025-08-03 14:34:00 [DEBUG] 控件映射: button4 -> '打开配置目录'
2025-08-03 14:34:00 [DEBUG] 控件映射: button5 -> '最近打开文件'
2025-08-03 14:34:00 [DEBUG] 控件映射: button51ToolsV1 -> '51助手'
2025-08-03 14:34:00 [DEBUG] 控件映射: button51ToolsV1b -> '51助手'
2025-08-03 14:34:00 [DEBUG] 控件映射: button51ToolsV2b -> '51小工具v2'
2025-08-03 14:34:00 [DEBUG] 控件映射: button6 -> 'Excel修复'
2025-08-03 14:34:00 [DEBUG] 控件映射: button7 -> 'Wps/Excel切换'
2025-08-03 14:34:00 [DEBUG] 控件映射: button8 -> '订单文件生成kml图层'
2025-08-03 14:34:00 [DEBUG] 控件映射: button9 -> '最近打开文件'
2025-08-03 14:34:00 [DEBUG] 控件映射: buttonAboutHy -> '关于'
2025-08-03 14:34:00 [DEBUG] 控件映射: buttonAboutZn -> '关于'
2025-08-03 14:34:00 [DEBUG] 控件映射: buttonAI辅助填写 -> 'AI辅助填写'
2025-08-03 14:34:00 [DEBUG] 控件映射: buttonDevelopTest -> 'Test'
2025-08-03 14:34:00 [DEBUG] 控件映射: buttonini配置文件 -> 'ini配置文件'
2025-08-03 14:34:00 [DEBUG] 控件映射: buttonPPTHelper -> 'PPT助手'
2025-08-03 14:34:00 [DEBUG] 控件映射: buttonPPT生成修改转PDF_B -> 'PPT批量生成/修改/转PDF'
2025-08-03 14:34:00 [DEBUG] 控件映射: buttonVisioHelper -> 'Visio助手'
2025-08-03 14:34:00 [DEBUG] 控件映射: buttonWordHelper -> 'Word助手'
2025-08-03 14:34:00 [DEBUG] 控件映射: buttonWord生成修改转PDF_B -> 'Word批量生成/修改/转PDF'
2025-08-03 14:34:00 [DEBUG] 控件映射: buttonWpsExcel切换 -> 'Wps/Excel切换'
2025-08-03 14:34:00 [DEBUG] 控件映射: button标签填写筛选 -> '标签填写/筛选'
2025-08-03 14:34:00 [DEBUG] 控件映射: button打开脚本表 -> '打开脚本'
2025-08-03 14:34:00 [DEBUG] 控件映射: button打开文件 -> '打开文件'
2025-08-03 14:34:00 [DEBUG] 控件映射: button多边形GPS坐标转换器 -> '多边形GPS坐标转换器'
2025-08-03 14:34:00 [DEBUG] 控件映射: button复制当前文件路径 -> '复制路径'
2025-08-03 14:34:00 [DEBUG] 控件映射: button记录当前文件 -> '记录当前文件'
2025-08-03 14:34:00 [DEBUG] 控件映射: button考勤 -> '考勤'
2025-08-03 14:34:00 [DEBUG] 控件映射: button配置目录 -> '打开配置目录'
2025-08-03 14:34:00 [DEBUG] 控件映射: button批量找文件 -> '文件查找/复制/改名'
2025-08-03 14:34:00 [DEBUG] 控件映射: button清除全表条件格式 -> '清除全表条件格式'
2025-08-03 14:34:00 [DEBUG] 控件映射: button清除所选条件格式 -> '清除所选条件格式'
2025-08-03 14:34:00 [DEBUG] 控件映射: button取消条件格式并取消筛选 -> '清除所选条件格式及筛选'
2025-08-03 14:34:00 [DEBUG] 控件映射: button生成地理图层 -> '生成地理图层'
2025-08-03 14:34:00 [DEBUG] 控件映射: button铁塔KML点图转换 -> '铁塔KML点图转换'
2025-08-03 14:34:00 [DEBUG] 控件映射: button通过GPS计算最近站点 -> '批量查找站点'
2025-08-03 14:34:00 [DEBUG] 控件映射: button同步数据 -> '同步数据'
2025-08-03 14:34:00 [DEBUG] 控件映射: button外部链接 -> '删除外部链接'
2025-08-03 14:34:00 [DEBUG] 控件映射: button文件操作 -> '文件查找/复制/改名'
2025-08-03 14:34:00 [DEBUG] 控件映射: button向下填充 -> '向下填充'
2025-08-03 14:34:00 [DEBUG] 控件映射: button重置单元格备注大小 -> '重置单元格备注大小'
2025-08-03 14:34:00 [DEBUG] 控件映射: checkBoxHorizontalHighlight -> '水平高亮行列'
2025-08-03 14:34:00 [DEBUG] 控件映射: checkBoxStockHelper -> 'StockHelper'
2025-08-03 14:34:00 [DEBUG] 控件映射: checkBoxVerticalHighlight -> '垂直高亮行列'
2025-08-03 14:34:00 [DEBUG] 控件映射: checkBox叠加显示辅助 -> '叠加显示辅助'
2025-08-03 14:34:00 [DEBUG] 控件映射: checkBox分级标记 -> '分级标记'
2025-08-03 14:34:00 [DEBUG] 控件映射: checkBox监控剪贴板 -> '监控剪贴板'
2025-08-03 14:34:00 [DEBUG] 控件映射: chk显示0值 -> '显示0值'
2025-08-03 14:34:00 [DEBUG] 控件映射: gallery常用文件 -> '常用文件'
2025-08-03 14:34:00 [DEBUG] 控件映射: gallery脚本内容 -> '脚本内容'
2025-08-03 14:34:00 [DEBUG] 控件映射: group1 -> '关于'
2025-08-03 14:34:00 [DEBUG] 控件映射: group2 -> '脚本'
2025-08-03 14:34:00 [DEBUG] 控件映射: groupOffice -> 'Office'
2025-08-03 14:34:00 [DEBUG] 控件映射: group标记标签 -> '标记标签'
2025-08-03 14:34:00 [DEBUG] 控件映射: group数据处理 -> '数据处理'
2025-08-03 14:34:00 [DEBUG] 控件映射: group文件 -> '文件'
2025-08-03 14:34:00 [DEBUG] 控件映射: group无线 -> '无线'
2025-08-03 14:34:00 [DEBUG] 控件映射: group字符格式 -> '字符/格式'
2025-08-03 14:34:00 [DEBUG] 控件映射: hy_group其它 -> '其它'
2025-08-03 14:34:00 [DEBUG] 控件映射: hy_menu设置 -> '设置'
2025-08-03 14:34:00 [DEBUG] 控件映射: hyTab -> 'Develop'
2025-08-03 14:34:00 [DEBUG] 控件映射: menu1 -> '基站数据处理'
2025-08-03 14:34:00 [DEBUG] 控件映射: menu2 -> '其它'
2025-08-03 14:34:00 [DEBUG] 控件映射: menu3 -> '设置'
2025-08-03 14:34:00 [DEBUG] 控件映射: menu5 -> '修复'
2025-08-03 14:34:00 [DEBUG] 控件映射: menuHY -> '其它'
2025-08-03 14:34:00 [DEBUG] 控件映射: menu其它3 -> '其它'
2025-08-03 14:34:00 [DEBUG] 控件映射: menu设置其它 -> '其它'
2025-08-03 14:34:00 [DEBUG] 控件映射: menu修复 -> '修复'
2025-08-03 14:34:00 [DEBUG] 控件映射: zn_groupOffice -> 'Office'
2025-08-03 14:34:00 [DEBUG] 控件映射: zn_group其它 -> '其它'
2025-08-03 14:34:00 [DEBUG] 控件映射: zn_group文件 -> '文件'
2025-08-03 14:34:00 [DEBUG] 控件映射: zn_group无线 -> '无线'
2025-08-03 14:34:00 [DEBUG] 控件映射: zn_group字符格式 -> '字符/格式'
2025-08-03 14:34:00 [DEBUG] 控件映射: znAbout -> 'ZnAbout'
2025-08-03 14:34:00 [DEBUG] 控件映射: znAboutButton -> '授权'
2025-08-03 14:34:00 [DEBUG] 控件映射: znAboutGroup -> '授权'
2025-08-03 14:34:00 [DEBUG] 控件映射: znTab -> 'ZnTools'
2025-08-03 14:34:00 [DEBUG] 获取到权限UI映射: 2 个权限组
2025-08-03 14:34:00 [DEBUG] 开始动态生成控件权限映射（全局一次性创建）
2025-08-03 14:34:00 [DEBUG] 开始生成控件权限映射
2025-08-03 14:34:00 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-08-03 14:34:00 [DEBUG] 通过反射获取到 124 个字段
2025-08-03 14:34:00 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-08-03 14:34:00 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-08-03 14:34:00 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-08-03 14:34:00 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-08-03 14:34:00 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-08-03 14:34:00 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [INFO] 控件结构获取完成，共获取到 122 个控件
2025-08-03 14:34:00 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-08-03 14:34:00 [INFO] 控件权限映射生成完成，共生成 116 项映射
2025-08-03 14:34:00 [DEBUG] 全局控件权限映射生成完成，共生成 116 项
2025-08-03 14:34:00 [INFO] 关键控件权限映射: hyTab -> hyex_dev
2025-08-03 14:34:00 [INFO] 关键控件权限映射: znTab -> hyex_user
2025-08-03 14:34:00 [INFO] 全局控件映射初始化完成 - 标题映射: 109 项, 权限映射: 116 项
2025-08-03 14:34:00 [DEBUG] 批量注册控件权限映射完成，成功: 116/116
2025-08-03 14:34:00 [DEBUG] HyExcel控件权限映射注册完成，共注册 116 个控件
2025-08-03 14:34:00 [INFO] 开始初始化权限验证
2025-08-03 14:34:00 [DEBUG] 设置默认UI可见性为false
2025-08-03 14:34:00 [DEBUG] 开始检查所有需要的权限
2025-08-03 14:34:00 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-08-03 14:34:01 [INFO] 启动网络授权信息获取任务
2025-08-03 14:34:01 [INFO] 授权信息刷新成功，版本: 1.0, 颁发者: ExtensionsTools
2025-08-03 14:34:01 [INFO] 所有权限检查完成
2025-08-03 14:34:01 [DEBUG] 应用权限状态到UI控件
2025-08-03 14:34:01 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 14:34:01 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 14:34:01 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:34:01 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 14:34:01 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 14:34:01 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:34:01 [DEBUG] 已应用权限状态到UI控件
2025-08-03 14:34:01 [DEBUG] 启动后台权限刷新任务
2025-08-03 14:34:01 [DEBUG] 启动延迟权限刷新任务
2025-08-03 14:34:01 [INFO] 权限验证初始化完成
2025-08-03 14:34:01 [INFO] UI权限管理初始化完成
2025-08-03 14:34:01 [INFO] 收到权限管理器初始化完成通知
2025-08-03 14:34:01 [INFO] 开始刷新控件标题
2025-08-03 14:34:01 [DEBUG] 开始刷新所有控件权限状态
2025-08-03 14:34:01 [DEBUG] 控件权限状态刷新完成，已检查 116 个控件
2025-08-03 14:34:01 [DEBUG] 控件标题刷新完成
2025-08-03 14:34:01 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-03 14:34:01 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-03 14:34:01 [DEBUG] 🔍 HyRibbon反射获取到 124 个字段
2025-08-03 14:34:01 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 14:34:01 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-03 14:34:01 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-03 14:34:01 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-03 14:34:01 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-03 14:34:01 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-03 14:34:01 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-03 14:34:01 [INFO] 动态获取到 122 个Ribbon控件引用
2025-08-03 14:34:01 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 14:34:01 [INFO] 开始批量更新控件标题，共 122 个控件
2025-08-03 14:34:01 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-03 14:34:01 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-03 14:34:01 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-03 14:34:01 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-03 14:34:01 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-03 14:34:01 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-03 14:34:01 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-03 14:34:01 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-03 14:34:01 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-03 14:34:01 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-03 14:34:01 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-03 14:34:01 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-03 14:34:01 [INFO] 批量更新控件标题完成，成功更新 109 个控件
2025-08-03 14:34:01 [INFO] 动态批量更新完成，共更新 122 个控件
2025-08-03 14:34:01 [INFO] 控件标题更正完成
2025-08-03 14:34:01 [INFO] 控件标题刷新完成
2025-08-03 14:34:01 [INFO] 权限管理器初始化完成处理结束
2025-08-03 14:34:01 [DEBUG] HyExcel UI权限管理器初始化完成
2025-08-03 14:34:01 [DEBUG] 授权验证初始化完成
2025-08-03 14:34:01 [INFO] 模板文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\.template\hyExcelDnaData.xlsx
2025-08-03 14:34:01 [INFO] 成功加载配置和授权信息
2025-08-03 14:34:01 [INFO] 开始初始化定时器和设置
2025-08-03 14:34:01 [INFO] 定时器和设置初始化完成
2025-08-03 14:34:01 [INFO] 开始VSTO插件启动流程
2025-08-03 14:34:01 [INFO] TopMostForm窗体加载完成
2025-08-03 14:34:01 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 14:34:01 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 12329192
2025-08-03 14:34:01 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 12329192)
2025-08-03 14:34:01 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 12329192
2025-08-03 14:34:01 [INFO] 系统事件监控已启动
2025-08-03 14:34:01 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 14:34:01 [INFO] OpenForm: 窗体标题已设置为类名 'CrosshairOverlayForm'
2025-08-03 14:34:01 [INFO] OpenForm: 准备打开窗体 'CrosshairOverlayForm'，位置: Outside，单实例: True
2025-08-03 14:34:01 [INFO] 开始显示窗体 'CrosshairOverlayForm'，位置模式: Outside
2025-08-03 14:34:01 [INFO] 窗体 'CrosshairOverlayForm' 以TopMostForm为父窗体显示
2025-08-03 14:34:01 [INFO] 窗体 'CrosshairOverlayForm' 显示完成，句柄: 16066100
2025-08-03 14:34:01 [INFO] OpenForm: 窗体 'CrosshairOverlayForm' 打开成功
2025-08-03 14:34:01 [INFO] VSTO插件启动流程完成
2025-08-03 14:34:02 [INFO] 从Remote成功获取到网络授权信息
2025-08-03 14:34:02 [INFO] 网络授权信息已更新并触发回调
2025-08-03 14:34:02 [INFO] 网络授权信息已从 Network 更新
2025-08-03 14:34:02 [INFO] 授权版本: 1.0
2025-08-03 14:34:02 [INFO] 颁发者: ExtensionsTools
2025-08-03 14:34:02 [INFO] 用户数量: 3
2025-08-03 14:34:02 [INFO] 分组权限数量: 2
2025-08-03 14:34:02 [WARN] 配置文件中未找到用户组信息
2025-08-03 14:34:02 [INFO] 已重新设置用户组: []
2025-08-03 14:34:02 [INFO] 用户组信息已重新设置
2025-08-03 14:34:02 [INFO] 立即刷新权限缓存和UI界面
2025-08-03 14:34:02 [INFO] 开始强制刷新权限缓存和UI界面
2025-08-03 14:34:02 [DEBUG] 使用新的权限管理器进行强制刷新
2025-08-03 14:34:02 [DEBUG] 开始强制刷新HyExcel权限缓存和UI界面
2025-08-03 14:34:02 [INFO] 开始强制刷新权限缓存和UI界面
2025-08-03 14:34:02 [DEBUG] 本地权限缓存已清空
2025-08-03 14:34:02 [DEBUG] 跳过 LicenseController 刷新，避免死循环
2025-08-03 14:34:02 [INFO] 所有权限检查完成
2025-08-03 14:34:02 [DEBUG] 权限重新检查完成
2025-08-03 14:34:02 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 14:34:02 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:34:02 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 14:34:02 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:34:02 [DEBUG] 已应用权限状态到UI控件
2025-08-03 14:34:02 [INFO] UI界面权限状态已更新
2025-08-03 14:34:02 [DEBUG] 开始刷新所有控件权限状态
2025-08-03 14:34:02 [DEBUG] 控件权限状态刷新完成，已检查 116 个控件
2025-08-03 14:34:02 [DEBUG] HyExcel权限缓存和UI界面强制刷新完成
2025-08-03 14:34:02 [INFO] 权限缓存和UI界面立即刷新完成
2025-08-03 14:34:02 [INFO] 网络授权已更新，开始刷新控件标题
2025-08-03 14:34:02 [INFO] 开始刷新Ribbon控件标题
2025-08-03 14:34:02 [DEBUG] 权限缓存已清空，清除了 116 个缓存项
2025-08-03 14:34:02 [DEBUG] 开始刷新HyExcel Ribbon控件标题
2025-08-03 14:34:02 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-03 14:34:02 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-03 14:34:02 [DEBUG] 🔍 HyRibbon反射获取到 124 个字段
2025-08-03 14:34:02 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 14:34:02 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-03 14:34:02 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-03 14:34:02 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-03 14:34:02 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-03 14:34:02 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-03 14:34:02 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-03 14:34:02 [INFO] 动态获取到 122 个Ribbon控件引用
2025-08-03 14:34:02 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 14:34:02 [INFO] 开始批量更新控件标题，共 122 个控件
2025-08-03 14:34:02 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-03 14:34:02 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-03 14:34:02 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-03 14:34:02 [INFO] 批量更新控件标题完成，成功更新 109 个控件
2025-08-03 14:34:02 [INFO] 动态批量更新完成，共更新 122 个控件
2025-08-03 14:34:02 [INFO] 控件标题更正完成
2025-08-03 14:34:02 [DEBUG] HyExcel Ribbon控件标题刷新完成
2025-08-03 14:34:02 [INFO] Ribbon控件标题刷新完成
2025-08-03 14:34:02 [INFO] 控件标题刷新完成
2025-08-03 14:34:02 [DEBUG] Ribbon控件标题已刷新
2025-08-03 14:34:02 [INFO] 开始刷新控件标题
2025-08-03 14:34:02 [DEBUG] 开始刷新所有控件权限状态
2025-08-03 14:34:02 [DEBUG] 控件权限状态刷新完成，已检查 116 个控件
2025-08-03 14:34:02 [DEBUG] 控件标题刷新完成
2025-08-03 14:34:02 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-03 14:34:02 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-03 14:34:02 [DEBUG] 🔍 HyRibbon反射获取到 124 个字段
2025-08-03 14:34:02 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 14:34:02 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-03 14:34:02 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-03 14:34:02 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-03 14:34:02 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-03 14:34:02 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-03 14:34:02 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-03 14:34:02 [INFO] 动态获取到 122 个Ribbon控件引用
2025-08-03 14:34:02 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 14:34:02 [INFO] 开始批量更新控件标题，共 122 个控件
2025-08-03 14:34:02 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-03 14:34:02 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-03 14:34:02 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-03 14:34:02 [INFO] 批量更新控件标题完成，成功更新 109 个控件
2025-08-03 14:34:02 [INFO] 动态批量更新完成，共更新 122 个控件
2025-08-03 14:34:02 [INFO] 控件标题更正完成
2025-08-03 14:34:02 [INFO] 控件标题刷新完成
2025-08-03 14:34:02 [DEBUG] Ribbon控件标题已立即刷新
2025-08-03 14:34:02 [INFO] 开始刷新授权状态
2025-08-03 14:34:02 [DEBUG] 开始初始化授权验证
2025-08-03 14:34:02 [DEBUG] 使用新的权限管理器进行初始化
2025-08-03 14:34:02 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-08-03 14:34:02 [INFO] 开始初始化UI权限管理
2025-08-03 14:34:02 [DEBUG] [实例ID: 1208ccfd] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-08-03 14:34:02 [DEBUG] 🔍 [实例ID: 1208ccfd] 字典引用一致性检查:
2025-08-03 14:34:02 [DEBUG] 🔍   标题映射一致性: True
2025-08-03 14:34:02 [DEBUG] 🔍   权限映射一致性: True
2025-08-03 14:34:02 [DEBUG] 🔍   信息映射一致性: True
2025-08-03 14:34:02 [DEBUG] 🔍   特殊控件一致性: True
2025-08-03 14:34:02 [DEBUG] 控件权限管理器初始化完成 [实例ID: 1208ccfd]
2025-08-03 14:34:02 [DEBUG] 开始注册控件权限映射
2025-08-03 14:34:02 [DEBUG] 批量注册控件权限映射完成，成功: 116/116
2025-08-03 14:34:02 [DEBUG] HyExcel控件权限映射注册完成，共注册 116 个控件
2025-08-03 14:34:02 [INFO] 开始初始化权限验证
2025-08-03 14:34:02 [DEBUG] 设置默认UI可见性为false
2025-08-03 14:34:02 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 14:34:03 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 12329192
2025-08-03 14:34:03 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 12329192)
2025-08-03 14:34:03 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 14:34:03 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-08-03 14:34:03 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 14:34:03 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:34:03 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 14:34:03 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:34:03 [DEBUG] 已应用权限状态到UI控件
2025-08-03 14:34:03 [DEBUG] 开始重置 208 个命令栏
2025-08-03 14:34:03 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 14:34:03 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:34:03 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 14:34:03 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:34:03 [DEBUG] 已应用权限状态到UI控件
2025-08-03 14:34:03 [DEBUG] 开始检查所有需要的权限
2025-08-03 14:34:03 [INFO] 所有权限检查完成
2025-08-03 14:34:03 [DEBUG] 应用权限状态到UI控件
2025-08-03 14:34:03 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 14:34:03 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:34:03 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 14:34:03 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:34:03 [DEBUG] 已应用权限状态到UI控件
2025-08-03 14:34:03 [DEBUG] 启动后台权限刷新任务
2025-08-03 14:34:03 [DEBUG] 启动延迟权限刷新任务
2025-08-03 14:34:03 [INFO] 权限验证初始化完成
2025-08-03 14:34:03 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 14:34:03 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:34:03 [INFO] UI权限管理初始化完成
2025-08-03 14:34:03 [INFO] 收到权限管理器初始化完成通知
2025-08-03 14:34:03 [INFO] 开始刷新控件标题
2025-08-03 14:34:03 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 14:34:03 [DEBUG] 开始刷新所有控件权限状态
2025-08-03 14:34:03 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 14:34:03 [DEBUG] 控件权限状态刷新完成，已检查 116 个控件
2025-08-03 14:34:03 [DEBUG] 控件标题刷新完成
2025-08-03 14:34:03 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-03 14:34:03 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:34:03 [DEBUG] 🔍 HyRibbon反射获取到 124 个字段
2025-08-03 14:34:03 [DEBUG] 已应用权限状态到UI控件
2025-08-03 14:34:03 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 14:34:03 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-03 14:34:03 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-03 14:34:03 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-03 14:34:03 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-03 14:34:03 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-03 14:34:03 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-03 14:34:03 [INFO] 动态获取到 122 个Ribbon控件引用
2025-08-03 14:34:03 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 14:34:03 [INFO] 开始批量更新控件标题，共 122 个控件
2025-08-03 14:34:03 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-03 14:34:03 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-03 14:34:03 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-03 14:34:03 [INFO] 批量更新控件标题完成，成功更新 109 个控件
2025-08-03 14:34:03 [INFO] 动态批量更新完成，共更新 122 个控件
2025-08-03 14:34:03 [INFO] 控件标题更正完成
2025-08-03 14:34:03 [INFO] 控件标题刷新完成
2025-08-03 14:34:03 [INFO] 权限管理器初始化完成处理结束
2025-08-03 14:34:03 [DEBUG] HyExcel UI权限管理器初始化完成
2025-08-03 14:34:03 [DEBUG] 授权验证初始化完成
2025-08-03 14:34:03 [INFO] 授权状态刷新完成
2025-08-03 14:34:04 [DEBUG] 重置命令栏: cell
2025-08-03 14:34:04 [DEBUG] 重置命令栏: column
2025-08-03 14:34:04 [DEBUG] 重置命令栏: row
2025-08-03 14:34:04 [DEBUG] 重置命令栏: cell
2025-08-03 14:34:04 [DEBUG] 重置命令栏: column
2025-08-03 14:34:04 [DEBUG] 重置命令栏: row
2025-08-03 14:34:04 [DEBUG] 重置命令栏: row
2025-08-03 14:34:04 [DEBUG] 重置命令栏: column
2025-08-03 14:34:05 [DEBUG] 命令栏重置完成: 成功 8 个，失败 0 个
2025-08-03 14:34:05 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 14:34:05 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 14:34:05 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:34:05 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 14:34:05 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 14:34:05 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:34:05 [DEBUG] 已应用权限状态到UI控件
2025-08-03 14:34:07 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 14:34:07 [DEBUG] 授权控制器已初始化
2025-08-03 14:34:07 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 14:34:08 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 14:34:08 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 14:34:08 [DEBUG] 授权控制器已初始化
2025-08-03 14:34:08 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 14:34:09 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 14:34:09 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 14:34:09 [DEBUG] 授权控制器已初始化
2025-08-03 14:34:09 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 14:34:10 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 14:34:10 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 14:34:10 [DEBUG] 授权控制器已初始化
2025-08-03 14:34:10 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 14:34:11 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 14:34:11 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 14:34:11 [DEBUG] 授权控制器已初始化
2025-08-03 14:34:11 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 14:34:12 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 14:34:12 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 14:34:12 [DEBUG] 授权控制器已初始化
2025-08-03 14:34:12 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 14:34:13 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 14:34:13 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 14:34:13 [DEBUG] 授权控制器已初始化
2025-08-03 14:34:13 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 14:34:14 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 14:34:15 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 14:34:15 [DEBUG] 授权控制器已初始化
2025-08-03 14:34:15 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
